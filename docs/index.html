<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Blog API v3 Documentation</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="description" content="Complete documentation for Blog API v3 - A modern, scalable blog platform built with Go">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css">
</head>
<body>
  <div id="app"></div>
  <script>
    window.$docsify = {
      name: 'Blog API v3',
      repo: '',
      loadSidebar: true,
      loadNavbar: true,
      subMaxLevel: 3,
      auto2top: true,
      search: {
        paths: 'auto',
        placeholder: 'Search documentation...',
        noData: 'No results found.',
        depth: 6
      },
      pagination: {
        previousText: 'Previous',
        nextText: 'Next',
        crossChapter: true
      },
      copyCode: {
        buttonText: 'Copy',
        errorText: 'Error',
        successText: 'Copied!'
      },
      tabs: {
        persist: true,
        sync: true,
        theme: 'classic',
        tabComments: true,
        tabHeadings: true
      }
    }
  </script>
  <!-- Docsify v4 -->
  <script src="//cdn.jsdelivr.net/npm/docsify@4"></script>
  <!-- Search plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"></script>
  <!-- Copy code plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify-copy-code@2"></script>
  <!-- Pagination plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify-pagination@2/dist/docsify-pagination.min.js"></script>
  <!-- Tabs plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify-tabs@1"></script>
  <!-- Mermaid support -->
  <script src="//cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true });
  </script>
  <script src="//cdn.jsdelivr.net/npm/docsify-mermaid@2/dist/docsify-mermaid.js"></script>
  <!-- Mermaid Zoom plugin -->
  <script src="//cdn.jsdelivr.net/gh/corentinleberre/docsify-mermaid-zoom@latest/dist/docsify-mermaid-zoom.min.js"></script>
  <!-- Prism syntax highlighting -->
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-go.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-yaml.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-json.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-sql.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-bash.min.js"></script>
</body>
</html>