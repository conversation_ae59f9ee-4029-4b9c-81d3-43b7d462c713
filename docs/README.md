# Blog API v3 Documentation

## Overview

This directory contains comprehensive documentation for the Blog API v3 project. The documentation is organized into focused topics for easy navigation and maintenance.

## Documentation Structure

### 🏗️ Architecture
- [Overview](./architecture/overview.md) - High-level system architecture
- [Project Structure](./architecture/project-structure.md) - Complete directory and file organization
- [Multi-Tenant User Management](./architecture/multi-tenant-user-management.md) - Cross-tenant user management with role-based permissions
- [Tenant Priority System](./architecture/tenant-priority-system.md) - Priority system for different tenant plans
- [Logging System](./architecture/logging-system.md) - Comprehensive logging with debug/production modes
- [Inter-module Communication](./architecture/inter-module-communication.md) - Module communication patterns
- [Queue System](./architecture/queue-system.md) - Asynchronous processing system
- [Cron & Scheduler](./architecture/cron-scheduler.md) - Scheduled jobs and automation

### 🗄️ Database
- [Database Design](./database/database-design.md) - Database schema design standards and conventions
- [Tenant Initialization](./database/tenant-initialization.md) - Data initialization for new tenants
- [Migrations](./database/migrations.md) - Database migration system
- [Seeding](./database/seeding.md) - Database seeding and fixtures

### 🧩 Modules
- [Module System](./modules/overview.md) - Module architecture overview
- [Auth Module](./modules/auth.md) - Authentication and authorization
- [User Module](./modules/user.md) - User management, profiles, preferences, and social features
- [Blog Module](./modules/blog.md) - Blog management system with posts, categories, and tags
- [Blog Submission Flow](./modules/blog-submission-flow.md) - Detailed flow for blog post submission, review, and publishing
- [Notification Module](./modules/notification.md) - Multi-channel notification system with email templates and real-time delivery
- [Socket Module](./modules/socket.md) - Real-time communication system
- [Media Module](./modules/media.md) - Media file management system
- [RBAC Module](./modules/rbac.md) - Role-based access control
- [Tenant Module](./modules/tenant.md) - Multi-tenancy support
- [Website Module](./modules/website.md) - Frontend website management
- [Onboarding Module](./modules/onboarding.md) - User onboarding system
- [SEO Module](./modules/seo.md) - Search Engine Optimization
- [Payment Module](./modules/payment.md) - Payment processing integration

### 🔌 Plugins
- [Plugin System](./plugins/overview.md) - Plugin architecture and extensibility system
- [Creating Plugins](./plugins/creating-plugins.md) - Complete plugin development guide
- [Available Plugins](./plugins/available-plugins.md) - List of available plugins and roadmap

### 🌍 Features
- [Internationalization (i18n)](./features/i18n.md) - Multi-language support system

### 🔗 API Reference
- [Response Standard](./api/response-standard.md) - Standardized response format for all APIs
- [CMS API](./api/cms-api.md) - Admin and content management API
- [Frontend API](./api/frontend-api.md) - Public website and content delivery API

### 👨‍💻 Development
- [Local Testing](./development/local-testing.md) - Complete local development environment with MailCatcher, Jaeger tracing, and timezone testing
- [Golang Libraries](./development/golang-libraries.md) - Comprehensive list of Go libraries used in the project
- [Testing](./development/testing.md) - Testing strategies and examples

## Quick Links

- [Getting Started](./development/getting-started.md) - Start here for new developers
- [Module Development](./modules/overview.md) - Learn about the module system
- [Database Migrations](./database/migrations.md) - Database management
- [API Reference](./api/overview.md) - API documentation

## Contributing

When adding new documentation:

1. Follow the existing structure and naming conventions
2. Use clear, concise language
3. Include code examples where appropriate
4. Update this README when adding new sections
5. Cross-reference related documentation

## Versioning

This documentation is version-controlled alongside the codebase. Each major release will have corresponding documentation updates.