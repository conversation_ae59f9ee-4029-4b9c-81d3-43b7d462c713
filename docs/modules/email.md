# Email Module - Tài liệu Tiếng Việt

## Tổng quan

Email Module cung cấp hệ thống gửi email toàn diện cho Blog API v3, hỗ trợ multi-tenancy với khả năng tùy chỉnh templates, queue processing, tracking, và analytics. Module được thiết kế để xử lý volume email lớn với độ tin cậy cao.

## Mục tiêu

- **Multi-tenant Email**: Hỗ trợ email riêng biệt cho từng website
- **Template Engine**: H<PERSON> thống templates linh hoạt và customizable
- **Queue Processing**: X<PERSON> lý email bất đồng bộ với queue
- **Delivery Tracking**: <PERSON> dõi trạng thái gửi email
- **Analytics**: Thống kê mở email, click, bounce, unsubscribe
- **Provider Integration**: Hỗ trợ nhiều email providers
- **Personalization**: <PERSON><PERSON> nhân hóa email theo user
- **Compliance**: <PERSON><PERSON> thủ GDPR, CAN-SPAM, CCPA

## Ki<PERSON>n trú<PERSON> hệ thống

### Email Module Architecture

```mermaid
flowchart TD
    A[Email <PERSON>dule] --> B[Template Engine]
    A --> C[Queue Manager]
    A --> D[Provider Manager]
    A --> E[Tracking System]
    A --> F[Analytics Engine]
    A --> G[Subscription Manager]
    
    B --> B1[Template Parser]
    B --> B2[Variable Injection]
    B --> B3[HTML/Text Generation]
    B --> B4[Template Validation]
    
    C --> C1[Job Queue]
    C --> C2[Priority Handling]
    C --> C3[Retry Logic]
    C --> C4[Dead Letter Queue]
    
    D --> D1[SMTP Provider]
    D --> D2[SendGrid]
    D --> D3[Mailgun]
    D --> D4[SES]
    
    E --> E1[Delivery Status]
    E --> E2[Open Tracking]
    E --> E3[Click Tracking]
    E --> E4[Bounce Handling]
    
    F --> F1[Engagement Metrics]
    F --> F2[Campaign Analytics]
    F --> F3[Performance Reports]
    F --> F4[A/B Testing]
    
    G --> G1[Subscription Lists]
    G --> G2[Preference Center]
    G --> G3[Unsubscribe Handling]
    G --> G4[Suppression Lists]
```

## Model Structures

### Database Schema

```mermaid
erDiagram
    EMAIL_TEMPLATE {
        uint id PK
        uint website_id FK
        string name
        string subject
        text html_content
        text text_content
        string category
        json variables
        string status
        uint created_by
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    EMAIL_QUEUE {
        uint id PK
        uint website_id FK
        uint template_id FK
        string recipient_email
        string recipient_name
        string subject
        text html_content
        text text_content
        json variables
        string priority
        string status
        int retry_count
        datetime scheduled_at
        datetime sent_at
        datetime created_at
        datetime updated_at
    }
    
    EMAIL_TRACKING {
        uint id PK
        uint website_id FK
        uint email_id FK
        string tracking_id
        string event_type
        string user_agent
        string ip_address
        json metadata
        datetime created_at
    }
    
    EMAIL_SUBSCRIPTION {
        uint id PK
        uint website_id FK
        uint user_id FK
        string email
        string status
        json preferences
        string unsubscribe_token
        datetime subscribed_at
        datetime unsubscribed_at
        datetime created_at
        datetime updated_at
    }
    
    EMAIL_CAMPAIGN {
        uint id PK
        uint website_id FK
        uint template_id FK
        string name
        string subject
        text content
        string status
        json target_criteria
        datetime scheduled_at
        datetime sent_at
        uint created_by
        datetime created_at
        datetime updated_at
    }
    
    WEBSITE ||--o{ EMAIL_TEMPLATE : "has"
    WEBSITE ||--o{ EMAIL_QUEUE : "has"
    EMAIL_TEMPLATE ||--o{ EMAIL_QUEUE : "uses"
    EMAIL_QUEUE ||--o{ EMAIL_TRACKING : "tracks"
    WEBSITE ||--o{ EMAIL_SUBSCRIPTION : "has"
    WEBSITE ||--o{ EMAIL_CAMPAIGN : "has"
```

### Models

```go
type EmailTemplate struct {
    ID           uint           `gorm:"primarykey" json:"id"`
    WebsiteID    uint           `gorm:"not null;index" json:"website_id"`
    Name         string         `gorm:"size:100;not null" json:"name"`
    Subject      string         `gorm:"size:255;not null" json:"subject"`
    HTMLContent  string         `gorm:"type:text" json:"html_content"`
    TextContent  string         `gorm:"type:text" json:"text_content"`
    Category     string         `gorm:"size:50" json:"category"`
    Variables    datatypes.JSON `gorm:"type:json" json:"variables"`
    Status       string         `gorm:"size:20;default:'active'" json:"status"`
    CreatedBy    uint           `gorm:"not null" json:"created_by"`
    CreatedAt    time.Time      `json:"created_at"`
    UpdatedAt    time.Time      `json:"updated_at"`
    DeletedAt    gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
    
    // Relations
    Website      Website        `json:"website,omitempty"`
    Emails       []EmailQueue   `json:"emails,omitempty"`
}

type EmailQueue struct {
    ID            uint           `gorm:"primarykey" json:"id"`
    WebsiteID     uint           `gorm:"not null;index" json:"website_id"`
    TemplateID    *uint          `gorm:"index" json:"template_id"`
    RecipientEmail string        `gorm:"size:255;not null" json:"recipient_email"`
    RecipientName  string        `gorm:"size:255" json:"recipient_name"`
    Subject       string         `gorm:"size:255;not null" json:"subject"`
    HTMLContent   string         `gorm:"type:text" json:"html_content"`
    TextContent   string         `gorm:"type:text" json:"text_content"`
    Variables     datatypes.JSON `gorm:"type:json" json:"variables"`
    Priority      string         `gorm:"size:20;default:'normal'" json:"priority"`
    Status        string         `gorm:"size:20;default:'pending'" json:"status"`
    RetryCount    int            `gorm:"default:0" json:"retry_count"`
    ScheduledAt   *time.Time     `json:"scheduled_at"`
    SentAt        *time.Time     `json:"sent_at"`
    CreatedAt     time.Time      `json:"created_at"`
    UpdatedAt     time.Time      `json:"updated_at"`
    
    // Relations
    Website       Website        `json:"website,omitempty"`
    Template      *EmailTemplate `json:"template,omitempty"`
    Tracking      []EmailTracking `json:"tracking,omitempty"`
}

type EmailTracking struct {
    ID          uint           `gorm:"primarykey" json:"id"`
    WebsiteID   uint           `gorm:"not null;index" json:"website_id"`
    EmailID     uint           `gorm:"not null;index" json:"email_id"`
    TrackingID  string         `gorm:"size:255;uniqueIndex" json:"tracking_id"`
    EventType   string         `gorm:"size:50;not null" json:"event_type"`
    UserAgent   string         `gorm:"size:500" json:"user_agent"`
    IPAddress   string         `gorm:"size:45" json:"ip_address"`
    Metadata    datatypes.JSON `gorm:"type:json" json:"metadata"`
    CreatedAt   time.Time      `json:"created_at"`
    
    // Relations
    Website     Website        `json:"website,omitempty"`
    Email       EmailQueue     `json:"email,omitempty"`
}

type EmailSubscription struct {
    ID               uint           `gorm:"primarykey" json:"id"`
    WebsiteID        uint           `gorm:"not null;index" json:"website_id"`
    UserID           *uint          `gorm:"index" json:"user_id"`
    Email            string         `gorm:"size:255;not null" json:"email"`
    Status           string         `gorm:"size:20;default:'active'" json:"status"`
    Preferences      datatypes.JSON `gorm:"type:json" json:"preferences"`
    UnsubscribeToken string         `gorm:"size:255;uniqueIndex" json:"unsubscribe_token"`
    SubscribedAt     *time.Time     `json:"subscribed_at"`
    UnsubscribedAt   *time.Time     `json:"unsubscribed_at"`
    CreatedAt        time.Time      `json:"created_at"`
    UpdatedAt        time.Time      `json:"updated_at"`
    
    // Relations
    Website          Website        `json:"website,omitempty"`
    User             *User          `json:"user,omitempty"`
}
```

## Email Flow Processes

### 1. Template-based Email Flow

```mermaid
sequenceDiagram
    participant Service as Application Service
    participant EmailSvc as Email Service
    participant Queue as Message Queue
    participant Worker as Email Worker
    participant Provider as Email Provider
    participant Tracking as Tracking Service
    
    Service->>EmailSvc: SendTemplateEmail(template_id, recipient, variables)
    EmailSvc->>EmailSvc: Load template from database
    EmailSvc->>EmailSvc: Render template with variables
    EmailSvc->>Queue: Enqueue email job
    Queue->>EmailSvc: Job queued
    EmailSvc->>Service: Email queued for delivery
    
    Queue->>Worker: Process email job
    Worker->>Worker: Validate recipient
    Worker->>Worker: Generate tracking ID
    Worker->>Worker: Inject tracking pixels
    Worker->>Provider: Send email
    Provider->>Worker: Delivery status
    Worker->>Tracking: Log delivery event
    
    alt Delivery successful
        Worker->>Queue: Mark job complete
    else Delivery failed
        Worker->>Queue: Retry or move to DLQ
    end
```

### 2. Direct Email Flow

```mermaid
sequenceDiagram
    participant Service as Application Service
    participant EmailSvc as Email Service
    participant Queue as Message Queue
    participant Worker as Email Worker
    participant Provider as Email Provider
    
    Service->>EmailSvc: SendDirectEmail(recipient, subject, content)
    EmailSvc->>EmailSvc: Validate email content
    EmailSvc->>Queue: Enqueue email job
    Queue->>EmailSvc: Job queued
    EmailSvc->>Service: Email queued for delivery
    
    Queue->>Worker: Process email job
    Worker->>Worker: Generate tracking ID
    Worker->>Worker: Inject tracking pixels
    Worker->>Provider: Send email
    Provider->>Worker: Delivery status
    Worker->>Worker: Update email status
    
    alt Delivery successful
        Worker->>Queue: Mark job complete
    else Delivery failed
        Worker->>Queue: Retry with exponential backoff
    end
```

### 3. Campaign Email Flow

```mermaid
sequenceDiagram
    participant Admin as Admin User
    participant CampaignSvc as Campaign Service
    participant EmailSvc as Email Service
    participant Queue as Message Queue
    participant Worker as Email Worker
    participant Analytics as Analytics Service
    
    Admin->>CampaignSvc: Create campaign
    CampaignSvc->>CampaignSvc: Define target audience
    CampaignSvc->>EmailSvc: Schedule campaign emails
    
    EmailSvc->>EmailSvc: Load recipients based on criteria
    EmailSvc->>Queue: Batch enqueue emails
    
    loop For each recipient
        Queue->>Worker: Process email job
        Worker->>Worker: Personalize content
        Worker->>Worker: Send email
        Worker->>Analytics: Track email sent
    end
    
    Analytics->>CampaignSvc: Update campaign stats
    CampaignSvc->>Admin: Campaign progress report
```

## Template System

### Template Structure

```go
type TemplateVariables struct {
    User      UserData              `json:"user"`
    Website   WebsiteData           `json:"website"`
    Content   ContentData           `json:"content"`
    Custom    map[string]interface{} `json:"custom"`
}

type UserData struct {
    ID        uint   `json:"id"`
    Name      string `json:"name"`
    Email     string `json:"email"`
    FirstName string `json:"first_name"`
    LastName  string `json:"last_name"`
}

type WebsiteData struct {
    Name    string `json:"name"`
    URL     string `json:"url"`
    Logo    string `json:"logo"`
    Support string `json:"support_email"`
}
```

### Template Examples

#### Welcome Email Template
```html
<!DOCTYPE html>
<html>
<head>
    <title>{{.Website.Name}} - Welcome!</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .header { background-color: #3498db; color: white; padding: 20px; }
        .content { padding: 20px; }
        .footer { background-color: #f8f9fa; padding: 10px; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Welcome to {{.Website.Name}}!</h1>
    </div>
    
    <div class="content">
        <p>Hi {{.User.FirstName}},</p>
        
        <p>Welcome to {{.Website.Name}}! We're excited to have you join our community.</p>
        
        <p>Here's what you can do next:</p>
        <ul>
            <li><a href="{{.Website.URL}}/profile">Complete your profile</a></li>
            <li><a href="{{.Website.URL}}/posts">Explore our latest posts</a></li>
            <li><a href="{{.Website.URL}}/settings">Customize your preferences</a></li>
        </ul>
        
        <p>If you have any questions, don't hesitate to reach out to us at {{.Website.Support}}.</p>
        
        <p>Best regards,<br>The {{.Website.Name}} Team</p>
    </div>
    
    <div class="footer">
        <p>&copy; {{.Website.Name}} | <a href="{{.Website.URL}}/unsubscribe?token={{.UnsubscribeToken}}">Unsubscribe</a></p>
    </div>
    
    <!-- Tracking pixel -->
    <img src="{{.TrackingURL}}" width="1" height="1" style="display:none;" />
</body>
</html>
```

#### Password Reset Template
```html
<!DOCTYPE html>
<html>
<head>
    <title>{{.Website.Name}} - Password Reset</title>
</head>
<body>
    <h2>Password Reset Request</h2>
    
    <p>Hi {{.User.FirstName}},</p>
    
    <p>We received a request to reset your password for your {{.Website.Name}} account.</p>
    
    <p>Click the button below to reset your password:</p>
    
    <a href="{{.Custom.ResetURL}}" style="background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
    
    <p>This link will expire in {{.Custom.ExpiresIn}} minutes.</p>
    
    <p>If you didn't request this password reset, please ignore this email.</p>
    
    <p>Best regards,<br>The {{.Website.Name}} Team</p>
</body>
</html>
```

## Email Service Implementation

### Email Service

```go
type EmailService struct {
    db           *gorm.DB
    queue        *Queue
    templateRepo *TemplateRepository
    provider     EmailProvider
    tracker      *TrackingService
}

// SendTemplateEmail sends an email using a template
func (s *EmailService) SendTemplateEmail(ctx context.Context, req *SendTemplateEmailRequest) error {
    // Load template
    template, err := s.templateRepo.GetByID(req.WebsiteID, req.TemplateID)
    if err != nil {
        return err
    }
    
    // Render template
    renderedEmail, err := s.renderTemplate(template, req.Variables)
    if err != nil {
        return err
    }
    
    // Create email job
    email := &EmailQueue{
        WebsiteID:      req.WebsiteID,
        TemplateID:     &req.TemplateID,
        RecipientEmail: req.RecipientEmail,
        RecipientName:  req.RecipientName,
        Subject:        renderedEmail.Subject,
        HTMLContent:    renderedEmail.HTMLContent,
        TextContent:    renderedEmail.TextContent,
        Variables:      req.Variables,
        Priority:       req.Priority,
        Status:         "pending",
        ScheduledAt:    req.ScheduledAt,
    }
    
    // Save to database
    if err := s.db.Create(email).Error; err != nil {
        return err
    }
    
    // Enqueue for processing
    job := &EmailJob{
        EmailID:   email.ID,
        WebsiteID: email.WebsiteID,
    }
    
    return s.queue.Enqueue("email", job, req.Priority)
}

// SendDirectEmail sends an email directly without template
func (s *EmailService) SendDirectEmail(ctx context.Context, req *SendDirectEmailRequest) error {
    // Create email job
    email := &EmailQueue{
        WebsiteID:      req.WebsiteID,
        RecipientEmail: req.RecipientEmail,
        RecipientName:  req.RecipientName,
        Subject:        req.Subject,
        HTMLContent:    req.HTMLContent,
        TextContent:    req.TextContent,
        Priority:       req.Priority,
        Status:         "pending",
        ScheduledAt:    req.ScheduledAt,
    }
    
    // Save to database
    if err := s.db.Create(email).Error; err != nil {
        return err
    }
    
    // Enqueue for processing
    job := &EmailJob{
        EmailID:   email.ID,
        WebsiteID: email.WebsiteID,
    }
    
    return s.queue.Enqueue("email", job, req.Priority)
}

// renderTemplate renders email template with variables
func (s *EmailService) renderTemplate(template *EmailTemplate, variables map[string]interface{}) (*RenderedEmail, error) {
    // Parse HTML template
    htmlTmpl, err := template.New("html").Parse(template.HTMLContent)
    if err != nil {
        return nil, err
    }
    
    // Parse text template
    textTmpl, err := template.New("text").Parse(template.TextContent)
    if err != nil {
        return nil, err
    }
    
    // Parse subject template
    subjectTmpl, err := template.New("subject").Parse(template.Subject)
    if err != nil {
        return nil, err
    }
    
    // Render HTML
    var htmlBuf bytes.Buffer
    if err := htmlTmpl.Execute(&htmlBuf, variables); err != nil {
        return nil, err
    }
    
    // Render text
    var textBuf bytes.Buffer
    if err := textTmpl.Execute(&textBuf, variables); err != nil {
        return nil, err
    }
    
    // Render subject
    var subjectBuf bytes.Buffer
    if err := subjectTmpl.Execute(&subjectBuf, variables); err != nil {
        return nil, err
    }
    
    return &RenderedEmail{
        Subject:     subjectBuf.String(),
        HTMLContent: htmlBuf.String(),
        TextContent: textBuf.String(),
    }, nil
}
```

### Email Worker

```go
type EmailWorker struct {
    db          *gorm.DB
    provider    EmailProvider
    tracker     *TrackingService
    maxRetries  int
}

// ProcessEmailJob processes an email job from the queue
func (w *EmailWorker) ProcessEmailJob(job *EmailJob) error {
    // Load email from database
    var email EmailQueue
    if err := w.db.First(&email, job.EmailID).Error; err != nil {
        return err
    }
    
    // Check if email is still pending
    if email.Status != "pending" {
        return nil // Already processed
    }
    
    // Generate tracking ID
    trackingID := w.generateTrackingID(email.ID)
    
    // Inject tracking pixels
    htmlContent := w.injectTrackingPixels(email.HTMLContent, trackingID)
    
    // Prepare email
    emailMsg := &EmailMessage{
        From:        w.getFromAddress(email.WebsiteID),
        To:          email.RecipientEmail,
        Subject:     email.Subject,
        HTMLContent: htmlContent,
        TextContent: email.TextContent,
        TrackingID:  trackingID,
    }
    
    // Send email
    err := w.provider.SendEmail(emailMsg)
    if err != nil {
        return w.handleFailure(&email, err)
    }
    
    // Update email status
    email.Status = "sent"
    email.SentAt = &time.Time{}
    *email.SentAt = time.Now()
    
    if err := w.db.Save(&email).Error; err != nil {
        return err
    }
    
    // Track delivery
    w.tracker.TrackEvent(email.WebsiteID, email.ID, trackingID, "delivered", nil)
    
    return nil
}

// handleFailure handles email sending failure
func (w *EmailWorker) handleFailure(email *EmailQueue, err error) error {
    email.RetryCount++
    
    if email.RetryCount >= w.maxRetries {
        email.Status = "failed"
    } else {
        email.Status = "retrying"
    }
    
    return w.db.Save(email).Error
}

// injectTrackingPixels injects tracking pixels into HTML content
func (w *EmailWorker) injectTrackingPixels(htmlContent, trackingID string) string {
    trackingPixel := fmt.Sprintf(`<img src="%s/track/%s/open" width="1" height="1" style="display:none;" />`, w.baseURL, trackingID)
    
    // Inject before closing body tag
    return strings.Replace(htmlContent, "</body>", trackingPixel+"</body>", 1)
}
```

### Email Providers

```go
// EmailProvider interface
type EmailProvider interface {
    SendEmail(msg *EmailMessage) error
    GetDeliveryStatus(messageID string) (*DeliveryStatus, error)
}

// SMTP Provider
type SMTPProvider struct {
    host     string
    port     int
    username string
    password string
}

func (p *SMTPProvider) SendEmail(msg *EmailMessage) error {
    auth := smtp.PlainAuth("", p.username, p.password, p.host)
    
    // Compose email
    to := []string{msg.To}
    body := p.composeEmail(msg)
    
    // Send email
    addr := fmt.Sprintf("%s:%d", p.host, p.port)
    return smtp.SendMail(addr, auth, msg.From, to, []byte(body))
}

// SendGrid Provider
type SendGridProvider struct {
    apiKey string
    client *sendgrid.Client
}

func (p *SendGridProvider) SendEmail(msg *EmailMessage) error {
    from := mail.NewEmail("", msg.From)
    to := mail.NewEmail(msg.RecipientName, msg.To)
    
    message := mail.NewSingleEmail(from, msg.Subject, to, msg.TextContent, msg.HTMLContent)
    
    // Add tracking
    message.SetTrackingSettings(&mail.TrackingSettings{
        ClickTracking: &mail.ClickTrackingSettings{
            Enable: mail.NewBool(true),
        },
        OpenTracking: &mail.OpenTrackingSettings{
            Enable: mail.NewBool(true),
        },
    })
    
    response, err := p.client.Send(message)
    if err != nil {
        return err
    }
    
    if response.StatusCode >= 400 {
        return fmt.Errorf("failed to send email: %s", response.Body)
    }
    
    return nil
}
```

## Tracking System

### Tracking Service

```go
type TrackingService struct {
    db    *gorm.DB
    cache *redis.Client
}

// TrackEvent tracks email events
func (s *TrackingService) TrackEvent(websiteID, emailID uint, trackingID, eventType string, metadata map[string]interface{}) error {
    tracking := &EmailTracking{
        WebsiteID:  websiteID,
        EmailID:    emailID,
        TrackingID: trackingID,
        EventType:  eventType,
        Metadata:   metadata,
        CreatedAt:  time.Now(),
    }
    
    // Save to database
    if err := s.db.Create(tracking).Error; err != nil {
        return err
    }
    
    // Update cache counters
    s.updateCacheCounters(websiteID, eventType)
    
    return nil
}

// TrackOpen tracks email opens
func (s *TrackingService) TrackOpen(trackingID string, userAgent, ipAddress string) error {
    // Get email info from tracking ID
    var email EmailQueue
    if err := s.db.Where("tracking_id = ?", trackingID).First(&email).Error; err != nil {
        return err
    }
    
    metadata := map[string]interface{}{
        "user_agent":  userAgent,
        "ip_address":  ipAddress,
        "opened_at":   time.Now(),
    }
    
    return s.TrackEvent(email.WebsiteID, email.ID, trackingID, "opened", metadata)
}

// TrackClick tracks email clicks
func (s *TrackingService) TrackClick(trackingID, url string, userAgent, ipAddress string) error {
    // Get email info from tracking ID
    var email EmailQueue
    if err := s.db.Where("tracking_id = ?", trackingID).First(&email).Error; err != nil {
        return err
    }
    
    metadata := map[string]interface{}{
        "url":         url,
        "user_agent":  userAgent,
        "ip_address":  ipAddress,
        "clicked_at":  time.Now(),
    }
    
    return s.TrackEvent(email.WebsiteID, email.ID, trackingID, "clicked", metadata)
}
```

### Tracking Handlers

```go
// Track email open
func (h *TrackingHandler) TrackOpen(c *gin.Context) {
    trackingID := c.Param("tracking_id")
    userAgent := c.GetHeader("User-Agent")
    ipAddress := c.ClientIP()
    
    // Track the open event
    if err := h.tracker.TrackOpen(trackingID, userAgent, ipAddress); err != nil {
        log.Printf("Failed to track open: %v", err)
    }
    
    // Return 1x1 transparent pixel
    pixel := []byte{
        0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00,
        0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x21, 0xF9, 0x04, 0x01, 0x00,
        0x00, 0x00, 0x00, 0x2C, 0x00, 0x00, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x00, 0x02, 0x02, 0x04,
        0x01, 0x00, 0x3B,
    }
    
    c.Header("Content-Type", "image/gif")
    c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
    c.Data(200, "image/gif", pixel)
}

// Track email click
func (h *TrackingHandler) TrackClick(c *gin.Context) {
    trackingID := c.Param("tracking_id")
    url := c.Query("url")
    userAgent := c.GetHeader("User-Agent")
    ipAddress := c.ClientIP()
    
    // Track the click event
    if err := h.tracker.TrackClick(trackingID, url, userAgent, ipAddress); err != nil {
        log.Printf("Failed to track click: %v", err)
    }
    
    // Redirect to original URL
    c.Redirect(302, url)
}
```

## API Endpoints

### Template Management

#### Create Email Template
```http
POST /api/cms/v1/email-templates
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "name": "Welcome Email",
  "subject": "Welcome to {{.Website.Name}}!",
  "html_content": "<html>...</html>",
  "text_content": "Welcome to {{.Website.Name}}!...",
  "category": "user_onboarding",
  "variables": {
    "user": ["name", "email"],
    "website": ["name", "url"],
    "custom": ["action_url"]
  }
}
```

#### Send Template Email
```http
POST /api/cms/v1/emails/send-template
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "template_id": 1,
  "recipient_email": "<EMAIL>",
  "recipient_name": "John Doe",
  "variables": {
    "user": {
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "custom": {
      "action_url": "https://example.com/verify?token=abc123"
    }
  },
  "priority": "high",
  "scheduled_at": "2024-07-15T10:00:00Z"
}
```

#### Send Direct Email
```http
POST /api/cms/v1/emails/send-direct
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "recipient_email": "<EMAIL>",
  "recipient_name": "John Doe",
  "subject": "Important Update",
  "html_content": "<html>...</html>",
  "text_content": "Important update...",
  "priority": "normal"
}
```

### Email Analytics

#### Get Email Statistics
```http
GET /api/cms/v1/emails/stats?period=7d
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "summary": {
    "total_sent": 15420,
    "total_delivered": 15000,
    "total_opened": 8500,
    "total_clicked": 2100,
    "total_bounced": 420,
    "total_unsubscribed": 50,
    "delivery_rate": 97.3,
    "open_rate": 56.7,
    "click_rate": 14.0,
    "bounce_rate": 2.7
  },
  "by_template": [
    {
      "template_id": 1,
      "template_name": "Welcome Email",
      "sent": 5000,
      "opened": 2850,
      "clicked": 750,
      "open_rate": 57.0,
      "click_rate": 15.0
    }
  ]
}
```

### Subscription Management

#### Subscribe User
```http
POST /api/cms/v1/email-subscriptions
Content-Type: application/json

{
  "email": "<EMAIL>",
  "preferences": {
    "newsletter": true,
    "product_updates": true,
    "marketing": false
  }
}
```

#### Unsubscribe
```http
POST /api/cms/v1/email-subscriptions/unsubscribe
Content-Type: application/json

{
  "token": "unsubscribe_token_abc123"
}
```

## Configuration

### Email Module Configuration
```yaml
email:
  enabled: true
  
  providers:
    default: "smtp"
    smtp:
      host: "smtp.gmail.com"
      port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      tls: true
    sendgrid:
      api_key: "${SENDGRID_API_KEY}"
    mailgun:
      api_key: "${MAILGUN_API_KEY}"
      domain: "${MAILGUN_DOMAIN}"
      
  queue:
    driver: "redis"
    max_retries: 3
    retry_delay: "5m"
    
  tracking:
    enabled: true
    base_url: "https://track.yourdomain.com"
    pixel_enabled: true
    click_tracking: true
    
  templates:
    cache_enabled: true
    cache_ttl: "1h"
    
  rate_limiting:
    enabled: true
    max_per_hour: 1000
    max_per_day: 10000
```

## Best Practices

### Email Deliverability
- **Authentication**: Configure SPF, DKIM, DMARC
- **Reputation**: Monitor sender reputation
- **List Hygiene**: Clean email lists regularly
- **Content Quality**: Avoid spam triggers
- **Feedback Loops**: Process bounces and complaints

### Performance Optimization
- **Queue Processing**: Use background job queues
- **Batch Processing**: Process emails in batches
- **Template Caching**: Cache compiled templates
- **Provider Failover**: Implement failover between providers
- **Rate Limiting**: Respect provider rate limits

### Security & Compliance
- **Data Protection**: Encrypt sensitive data
- **Consent Management**: Implement proper consent
- **Unsubscribe**: Honor unsubscribe requests
- **Audit Trail**: Log all email activities
- **Tenant Isolation**: Separate email data by website

## Tài liệu liên quan

- [Notification Module](./notification.md)
- [User Module](./user.md)
- [Auth Module](./auth.md)
- [Queue System](../infrastructure/queue.md)
- [Security Best Practices](../best-practices/security.md)