# Auth Module - Tài liệu Tiếng Việt

## Tổng quan

Auth Module cung cấp hệ thống xác thực và phân quyền toàn diện cho Blog API v3, bao gồm đăng ký, đ<PERSON><PERSON> nh<PERSON><PERSON>, quản lý JWT tokens, reset password, role-based access control, và các tính năng bảo mật nâng cao.

## Mục tiêu

- **User Authentication**: <PERSON><PERSON><PERSON> thực người dùng an toàn với JWT
- **Authorization**: Phân quyền dựa trên roles và permissions
- **Account Management**: Quản lý tài khoản và profile người dùng
- **Password Security**: Bảo mật mật khẩu với bcrypt và validation
- **Session Management**: Quản lý sessions và refresh tokens
- **Security Features**: Rate limiting, account lockout, audit logging
- **Multi-tenancy**: Hỗ trợ tenant isolation với website_id

## Kiến trú<PERSON> hệ thống

### Auth Module Architecture

```mermaid
flowchart TD
    A[Auth Module] --> B[Authentication Engine]
    A --> C[Authorization Engine]
    A --> D[Token Manager]
    A --> E[Password Manager]
    A --> F[Session Manager]
    A --> G[Security Monitor]
    
    B --> B1[Login/Register]
    B --> B2[JWT Validation]
    B --> B3[Multi-factor Auth]
    B --> B4[Social Login]
    
    C --> C1[Role-based Access]
    C --> C2[Permission Checks]
    C --> C3[Resource Authorization]
    C --> C4[Dynamic Permissions]
    
    D --> D1[JWT Generation]
    D --> D2[Token Refresh]
    D --> D3[Token Blacklist]
    D --> D4[Token Validation]
    
    E --> E1[Password Hashing]
    E --> E2[Password Validation]
    E --> E3[Password Reset]
    E --> E4[Password History]
    
    F --> F1[Session Creation]
    F --> F2[Session Tracking]
    F --> F3[Device Management]
    F --> F4[Concurrent Sessions]
    
    G --> G1[Failed Attempts]
    G --> G2[Suspicious Activity]
    G --> G3[Audit Logging]
    G --> G4[Rate Limiting]
```

## Authentication Flows với Module Integration

### 1. User Registration Flow với Full Module Integration

```mermaid
sequenceDiagram
    participant Client as Client App
    participant Gateway as API Gateway
    participant Auth as Auth Module
    participant Tenant as Tenant Module
    participant User as User Module
    participant RBAC as RBAC Module
    participant Notification as Notification Module
    participant Onboarding as Onboarding Module
    participant Media as Media Module
    participant Blog as Blog Module
    participant Socket as Socket Module
    participant Analytics as Analytics Module
    participant Queue as Message Queue
    participant Cache as Redis Cache
    participant DB as Database
    
    Note over Client,Gateway: Step 1: Registration Request
    Client->>Gateway: POST /api/cms/v1/auth/register
    Note over Client,Gateway: {first_name, last_name, email, password, website_id, role?, source?}
    
    Gateway->>Auth: Route to Auth Module
    
    Note over Auth,DB: Step 2: Input Validation & Pre-checks
    Auth->>Auth: Validate input format
    Auth->>Auth: Check password strength
    Auth->>Auth: Sanitize input data
    
    alt Input validation failed
        Auth->>Gateway: 422 Unprocessable Entity
        Gateway->>Client: Validation errors
    end
    
    Note over Auth,Tenant: Step 3: Tenant & Website Validation
    Auth->>Tenant: Validate website_id exists
    Tenant->>DB: SELECT * FROM blog_website WHERE id = ?
    DB->>Tenant: Website record
    
    alt Website not found or inactive
        Tenant->>Auth: Website invalid
        Auth->>Gateway: 404 Website not found
        Gateway->>Client: Error response
    end
    
    Tenant->>Auth: Website validated + tenant_id
    Auth->>Tenant: Check tenant limits
    Tenant->>DB: Get tenant user count
    DB->>Tenant: Current user count
    
    alt Tenant user limit exceeded
        Tenant->>Auth: Limit exceeded
        Auth->>Gateway: 403 Tenant limit reached
        Gateway->>Client: Upgrade plan required
    end
    
    Note over Auth,User: Step 4: Email Uniqueness Check
    Auth->>User: Check email exists in website
    User->>DB: SELECT COUNT(*) FROM blog_user WHERE email = ? AND website_id = ?
    DB->>User: Count result
    
    alt Email already exists
        User->>Auth: Email exists
        Auth->>Gateway: 409 Email already registered
        Gateway->>Client: Conflict error
    end
    
    Note over Auth,RBAC: Step 5: Role Validation
    Auth->>RBAC: Validate requested role (if provided)
    RBAC->>DB: SELECT * FROM blog_role WHERE slug = ? AND website_id = ?
    DB->>RBAC: Role record
    
    alt Invalid role provided
        RBAC->>Auth: Role not found
        Auth->>Gateway: 422 Invalid role
        Gateway->>Client: Validation error
    end
    
    RBAC->>Auth: Role validated or default role assigned
    
    Note over Auth,DB: Step 6: User Creation Transaction
    Auth->>DB: BEGIN TRANSACTION
    Auth->>Auth: Hash password with bcrypt
    Auth->>User: Create user record
    User->>DB: INSERT INTO blog_user (...)
    DB->>User: User created with ID
    User->>Auth: User ID returned
    
    Auth->>RBAC: Assign role to user
    RBAC->>DB: INSERT INTO blog_user_role (...)
    DB->>RBAC: Role assignment created
    
    Auth->>Auth: Generate JWT tokens
    Auth->>DB: INSERT INTO blog_user_session (...)
    DB->>Auth: Session stored
    
    Auth->>DB: COMMIT TRANSACTION
    
    Note over Auth,Cache: Step 7: Cache User Data
    Auth->>Cache: SET website:${website_id}:user:${user_id}
    Cache->>Auth: Cached successfully
    
    Auth->>Cache: SET website:${website_id}:user:email:${email}
    Cache->>Auth: Email mapping cached
    
    Note over Auth,Queue: Step 8: Publish Registration Event
    Auth->>Queue: Publish "user.registered" event
    Note over Queue: Event payload: {user_id, website_id, tenant_id, email, role, source, created_at}
    
    Note over Auth,Gateway: Step 9: Success Response
    Auth->>Gateway: 201 Created + user data + tokens
    Gateway->>Client: Registration successful
    Note over Gateway,Client: {user: {...}, access_token, refresh_token, expires_in}
    
    Note over Queue,Analytics: Step 10: Asynchronous Module Processing
    Queue->>User: user.registered event
    Queue->>Notification: user.registered event
    Queue->>Onboarding: user.registered event
    Queue->>Media: user.registered event
    Queue->>Blog: user.registered event
    Queue->>Socket: user.registered event
    Queue->>Analytics: user.registered event
    
    Note over User,DB: User Module Processing
    User->>DB: CREATE user_profile with defaults
    User->>DB: CREATE user_preferences with defaults
    User->>Cache: Cache user profile
    
    Note over Notification,Queue: Notification Module Processing
    Notification->>DB: CREATE welcome notification
    Notification->>Queue: Queue welcome email job
    Notification->>Queue: Queue onboarding email sequence
    
    Note over Onboarding,DB: Onboarding Module Processing
    Onboarding->>DB: CREATE onboarding_progress record
    Onboarding->>DB: Determine journey type based on role
    Onboarding->>Queue: Queue first onboarding step
    
    Note over Media,DB: Media Module Processing
    Media->>DB: CREATE user media folder structure
    Media->>Media: Set storage quota based on tenant plan
    Media->>Cache: Cache user media settings
    
    Note over Blog,DB: Blog Module Processing (if author role)
    alt User role is author/editor
        Blog->>DB: CREATE author_profile
        Blog->>DB: CREATE author draft workspace
        Blog->>Cache: Cache author settings
    end
    
    Note over Socket,Cache: Socket Module Processing
    Socket->>Cache: CREATE user websocket namespace
    Socket->>Socket: Initialize user notification channel
    Socket->>Socket: Set user online status
    
    Note over Analytics,DB: Analytics Module Processing
    Analytics->>DB: CREATE user_analytics record
    Analytics->>DB: Track registration event
    Analytics->>Analytics: Update tenant user metrics
    Analytics->>Cache: Cache user analytics data
    
    Note over Queue,Notification: Email Sending Process
    Queue->>Notification: Process welcome email job
    Notification->>Notification: Render email template
    Notification->>Notification: Send via email provider
    Notification->>DB: Log email sent status
    
    Note over Socket,Client: Real-time Notifications (if client connected)
    Socket->>Client: Welcome notification via WebSocket
    Socket->>Client: Onboarding progress update
```

#### Detailed Module Integration Flow

```mermaid
flowchart TD
    subgraph "Event: user.registered"
        A[Message Queue] --> B{Event Consumers}
    end
    
    subgraph "Synchronous Processing (Registration Request)"
        B --> C[Auth Module]
        C --> C1[Input Validation]
        C --> C2[Website Validation]
        C --> C3[Email Uniqueness Check]
        C --> C4[Role Assignment]
        C --> C5[User Creation]
        C --> C6[Token Generation]
        C --> C7[Session Storage]
        C --> C8[Cache User Data]
        C --> C9[Event Publishing]
    end
    
    subgraph "Asynchronous Processing (Background Jobs)"
        B --> D[User Module]
        D --> D1[Create Profile Record]
        D --> D2[Set Default Preferences]
        D --> D3[Initialize Settings]
        D --> D4[Setup Privacy Settings]
        D --> D5[Create Activity Timeline]
        
        B --> E[Notification Module]
        E --> E1[Send Welcome Email]
        E --> E2[Schedule Onboarding Sequence]
        E --> E3[Enable Default Subscriptions]
        E --> E4[Create Notification Preferences]
        E --> E5[Setup Email Templates]
        
        B --> F[Onboarding Module]
        F --> F1[Create Progress Record]
        F --> F2[Determine Journey Type]
        F --> F3[Start First Step]
        F --> F4[Schedule Follow-up Tasks]
        F --> F5[Track User Source]
        
        B --> G[Media Module]
        G --> G1[Create User Folder Structure]
        G --> G2[Set Storage Quota]
        G --> G3[Initialize Upload Settings]
        G --> G4[Create Default Avatar]
        G --> G5[Setup CDN Access]
        
        B --> H[Blog Module]
        H --> H1{Check Role}
        H1 -->|Author/Editor| H2[Create Author Profile]
        H1 -->|Author/Editor| H3[Setup Draft Workspace]
        H1 -->|Author/Editor| H4[Initialize Categories Access]
        H1 -->|Author/Editor| H5[Setup Writing Preferences]
        H1 -->|Reader| H6[Setup Reading Preferences]
        
        B --> I[Socket Module]
        I --> I1[Create User Namespace]
        I --> I2[Initialize Notification Channel]
        I --> I3[Setup Real-time Subscriptions]
        I --> I4[Create Chat Channels]
        I --> I5[Set Online Status]
        
        B --> J[Analytics Module]
        J --> J1[Create User Analytics Record]
        J --> J2[Track Registration Source]
        J --> J3[Setup Conversion Tracking]
        J --> J4[Initialize Metrics Collection]
        J --> J5[Update Tenant Statistics]
        
        B --> K[RBAC Module]
        K --> K1[Validate Role Assignment]
        K --> K2[Setup Permission Cache]
        K --> K3[Initialize Access Control]
        K --> K4[Create Audit Log]
        K --> K5[Setup Session Permissions]
    end
    
    subgraph "Real-time Updates"
        L[WebSocket Connections]
        I --> L
        L --> L1[Welcome Message]
        L --> L2[Onboarding Notifications]
        L --> L3[System Announcements]
        L --> L4[Tour Guidance]
    end
    
    subgraph "Email Automation"
        M[Email Queue]
        E --> M
        M --> M1[Welcome Email]
        M --> M2[Email Verification]
        M --> M3[Onboarding Series]
        M --> M4[Feature Announcements]
        M --> M5[Tips & Best Practices]
    end
```
    
    Note over Client,API: {user, access_token, refresh_token, expires_in}
```

### 2. User Login Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant Security as Security Monitor
    participant DB as Database
    participant JWT as JWT Service
    participant Cache as Redis Cache
    
    Client->>API: POST /auth/login
    Note over Client,API: {email, password, remember_me?, device_info?}
    
    API->>Security: Check rate limiting
    Security->>API: Rate limit status
    
    alt Rate limit exceeded
        API->>Client: 429 Too Many Requests
    end
    
    API->>DB: Get user by email and website_id
    DB->>API: User data
    
    alt User not found
        API->>Security: Log failed attempt
        API->>Client: 401 Invalid credentials
    end
    
    API->>API: Verify password with bcrypt
    
    alt Password incorrect
        API->>Security: Log failed attempt
        API->>Security: Check account lockout
        
        alt Too many failed attempts
            API->>DB: Lock account temporarily
            API->>Client: 423 Account locked
        else
            API->>Client: 401 Invalid credentials
        end
    end
    
    API->>DB: Check account status
    
    alt Account inactive/banned
        API->>Client: 403 Account not active
    end
    
    API->>JWT: Generate access + refresh tokens
    JWT->>API: Token pair
    
    API->>DB: Update last login time
    API->>DB: Store refresh token
    API->>Cache: Cache user session with website_id prefix
    API->>Security: Log successful login
    
    API->>Client: 200 Login success + tokens
    Note over Client,API: {user, access_token, refresh_token, expires_in}
```

### 3. Token Refresh Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant JWT as JWT Service
    participant DB as Database
    participant Blacklist as Token Blacklist
    
    Client->>API: POST /auth/refresh
    Note over Client,API: {refresh_token}
    
    API->>JWT: Validate refresh token format
    JWT->>API: Token validation result
    
    alt Token format invalid
        API->>Client: 401 Invalid token format
    end
    
    API->>Blacklist: Check if token blacklisted
    Blacklist->>API: Blacklist status
    
    alt Token blacklisted
        API->>Client: 401 Token revoked
    end
    
    API->>DB: Get refresh token from database
    DB->>API: Token record
    
    alt Token not found in DB
        API->>Client: 401 Invalid refresh token
    end
    
    API->>API: Check token expiration
    
    alt Token expired
        API->>DB: Delete expired token
        API->>Client: 401 Refresh token expired
    end
    
    API->>DB: Get user data
    DB->>API: User data
    
    alt User not found or inactive
        API->>DB: Delete token
        API->>Client: 401 User not found
    end
    
    API->>JWT: Generate new access token
    JWT->>API: New access token
    
    opt Token rotation enabled
        API->>JWT: Generate new refresh token
        JWT->>API: New refresh token
        API->>DB: Update refresh token
        API->>Blacklist: Add old refresh token to blacklist
    end
    
    API->>Client: 200 New tokens
    Note over Client,API: {access_token, refresh_token?, expires_in}
```

### 4. Password Reset Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service
    participant Token as Token Generator
    
    Note over Client,API: Step 1: Request Password Reset
    
    Client->>API: POST /auth/forgot-password
    Note over Client,API: {email}
    
    API->>DB: Find user by email
    DB->>API: User data
    
    alt User not found
        Note over API: Return success anyway (security)
        API->>Client: 200 Reset email sent
    end
    
    API->>Token: Generate reset token
    Token->>API: Secure reset token
    
    API->>DB: Store reset token with expiration
    API->>Email: Send reset email with token
    Email->>API: Email sent confirmation
    
    API->>Client: 200 Reset email sent
    
    Note over Client,API: Step 2: Reset Password with Token
    
    Client->>API: POST /auth/reset-password
    Note over Client,API: {token, new_password, password_confirmation}
    
    API->>DB: Find reset token
    DB->>API: Token record
    
    alt Token not found
        API->>Client: 404 Invalid reset token
    end
    
    API->>API: Check token expiration
    
    alt Token expired
        API->>DB: Delete expired token
        API->>Client: 410 Reset token expired
    end
    
    API->>API: Validate new password
    
    alt Password validation failed
        API->>Client: 422 Password validation error
    end
    
    API->>API: Hash new password
    API->>DB: Update user password
    API->>DB: Delete used reset token
    API->>DB: Invalidate all user sessions
    
    API->>Email: Send password changed notification
    API->>Client: 200 Password reset successful
```

### 5. Password Change Flow (Authenticated)

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant Auth as Auth Middleware
    participant DB as Database
    participant Email as Email Service
    
    Client->>API: POST /auth/change-password
    Note over Client,API: Authorization: Bearer <token>
    Note over Client,API: {current_password, new_password, new_password_confirmation}
    
    API->>Auth: Validate access token
    Auth->>API: User context
    
    alt Token invalid
        API->>Client: 401 Unauthorized
    end
    
    API->>DB: Get user data
    DB->>API: User record
    
    API->>API: Verify current password
    
    alt Current password incorrect
        API->>Client: 400 Current password incorrect
    end
    
    API->>API: Validate new password
    
    alt Password validation failed
        API->>Client: 422 Password validation error
    end
    
    API->>API: Check password history
    
    alt Password recently used
        API->>Client: 400 Password recently used
    end
    
    API->>API: Hash new password
    API->>DB: Update user password
    API->>DB: Add to password history
    API->>DB: Invalidate all refresh tokens (except current)
    
    API->>Email: Send password changed notification
    API->>Client: 200 Password changed successfully
```

### 6. Profile Management Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant Auth as Auth Middleware
    participant Validator as Input Validator
    participant DB as Database
    participant Media as Media Service
    participant Cache as Redis Cache
    
    Note over Client,API: Get Profile
    
    Client->>API: GET /auth/profile
    Note over Client,API: Authorization: Bearer <token>
    
    API->>Auth: Validate access token
    Auth->>API: User context
    
    API->>Cache: Check cached profile (website:{website_id}:user:{user_id})
    Cache->>API: Cached data (if exists)
    
    alt Cache miss
        API->>DB: Get user profile
        DB->>API: User data
        API->>Cache: Cache profile data with website_id prefix
    end
    
    API->>Client: 200 User profile
    
    Note over Client,API: Update Profile
    
    Client->>API: PUT /auth/profile
    Note over Client,API: {name, bio, avatar?, preferences}
    
    API->>Auth: Validate access token
    Auth->>API: User context
    
    API->>Validator: Validate input data
    Validator->>API: Validation result
    
    alt Validation failed
        API->>Client: 422 Validation errors
    end
    
    opt Avatar upload
        API->>Media: Process avatar upload
        Media->>API: Avatar URL
    end
    
    API->>DB: Update user profile
    DB->>API: Updated user data
    
    API->>Cache: Invalidate cached profile (website:{website_id}:user:{user_id})
    API->>Client: 200 Profile updated
```

### 7. Multi-Factor Authentication Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant DB as Database
    participant MFA as MFA Service
    participant TOTP as TOTP Generator
    participant SMS as SMS Service
    
    Note over Client,API: Enable MFA
    
    Client->>API: POST /auth/mfa/enable
    Note over Client,API: {method: "totp" | "sms"}
    
    API->>MFA: Generate MFA secret
    MFA->>API: Secret + QR code
    
    API->>DB: Store MFA secret (unconfirmed)
    API->>Client: 200 MFA setup data
    Note over Client,API: {secret, qr_code, backup_codes}
    
    Client->>API: POST /auth/mfa/confirm
    Note over Client,API: {code}
    
    API->>TOTP: Verify TOTP code
    TOTP->>API: Verification result
    
    alt Code invalid
        API->>Client: 400 Invalid MFA code
    end
    
    API->>DB: Confirm MFA setup
    API->>Client: 200 MFA enabled
    
    Note over Client,API: Login with MFA
    
    Client->>API: POST /auth/login
    Note over Client,API: {email, password}
    
    API->>API: Verify credentials
    API->>DB: Check MFA enabled
    
    alt MFA required
        API->>API: Generate temporary MFA token
        API->>Client: 200 MFA required
        Note over Client,API: {mfa_required: true, mfa_token}
        
        Client->>API: POST /auth/mfa/verify
        Note over Client,API: {mfa_token, mfa_code}
        
        API->>TOTP: Verify MFA code
        TOTP->>API: Verification result
        
        alt MFA code invalid
            API->>Client: 400 Invalid MFA code
        end
        
        API->>API: Complete login flow
        API->>Client: 200 Login successful + tokens
    else
        API->>Client: 200 Login successful + tokens
    end
```

### 8. Social Login Flow (OAuth)

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant OAuth as OAuth Provider
    participant DB as Database
    participant JWT as JWT Service
    
    Client->>API: GET /auth/oauth/{provider}/redirect
    API->>Client: 302 Redirect to OAuth provider
    
    Client->>OAuth: Authorization request
    OAuth->>Client: Authorization code
    
    Client->>API: GET /auth/oauth/{provider}/callback
    Note over Client,API: ?code=auth_code&state=csrf_token
    
    API->>API: Validate CSRF state
    
    alt Invalid state
        API->>Client: 400 Invalid state parameter
    end
    
    API->>OAuth: Exchange code for access token
    OAuth->>API: Access token + user info
    
    API->>OAuth: Get user profile
    OAuth->>API: User profile data
    
    API->>DB: Find user by OAuth ID
    DB->>API: User lookup result
    
    alt User exists
        API->>DB: Update last login
        API->>JWT: Generate tokens
        JWT->>API: Token pair
        API->>Client: 302 Redirect with tokens
    else User not exists
        alt Account linking enabled
            API->>DB: Find user by email
            DB->>API: Existing user (if any)
            
            alt Email user exists
                API->>DB: Link OAuth account
                API->>JWT: Generate tokens
                JWT->>API: Token pair
                API->>Client: 302 Redirect with tokens
            else Create new user
                API->>DB: Create user with OAuth data
                API->>JWT: Generate tokens
                JWT->>API: Token pair
                API->>Client: 302 Redirect with tokens
            end
        else
            API->>DB: Create user with OAuth data
            API->>JWT: Generate tokens
            JWT->>API: Token pair
            API->>Client: 302 Redirect with tokens
        end
    end
```

### 9. Session Management Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant DB as Database
    participant Cache as Redis Cache
    participant Monitor as Security Monitor
    
    Note over Client,API: List Active Sessions
    
    Client->>API: GET /auth/sessions
    Note over Client,API: Authorization: Bearer <token>
    
    API->>DB: Get user active sessions
    DB->>API: Session list
    
    API->>API: Enrich with device info
    API->>Client: 200 Active sessions
    Note over Client,API: [{session_id, device, location, last_active, current}]
    
    Note over Client,API: Terminate Session
    
    Client->>API: DELETE /auth/sessions/{session_id}
    
    API->>DB: Find session
    DB->>API: Session data
    
    alt Session not found or not owned
        API->>Client: 404 Session not found
    end
    
    alt Terminating current session
        API->>DB: Delete refresh token
        API->>Cache: Clear session cache
        API->>Client: 200 Current session terminated
    else Terminating other session
        API->>DB: Delete refresh token
        API->>Cache: Clear session cache
        API->>Monitor: Log session termination
        API->>Client: 200 Session terminated
    end
    
    Note over Client,API: Terminate All Sessions
    
    Client->>API: POST /auth/sessions/terminate-all
    
    API->>DB: Delete all user refresh tokens
    API->>Cache: Clear all user session caches
    API->>Monitor: Log mass session termination
    
    API->>Client: 200 All sessions terminated
```

### 10. Account Security Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Auth API
    participant Security as Security Monitor
    participant DB as Database
    participant Email as Email Service
    participant Admin as Admin Panel
    
    Note over Client,API: Suspicious Activity Detection
    
    Client->>API: Multiple failed login attempts
    
    API->>Security: Track failed attempts
    Security->>Security: Analyze patterns
    
    alt Suspicious pattern detected
        Security->>DB: Log security event
        Security->>Email: Send alert to user
        
        alt Severe threat detected
            Security->>DB: Temporarily lock account
            Security->>Admin: Alert administrators
            Security->>Email: Send account locked notification
        end
    end
    
    Note over Client,API: Account Recovery
    
    Client->>API: POST /auth/account-recovery
    Note over Client,API: {email, identity_verification}
    
    API->>Security: Verify recovery request
    Security->>API: Verification result
    
    alt Verification failed
        API->>Client: 403 Recovery verification failed
    end
    
    API->>DB: Generate recovery token
    API->>Email: Send recovery instructions
    API->>Client: 200 Recovery initiated
    
    Note over Client,API: Security Audit
    
    Client->>API: GET /auth/security-log
    
    API->>DB: Get user security events
    DB->>API: Security log
    
    API->>Client: 200 Security events
    Note over Client,API: [{event_type, timestamp, device, location, status}]
```

## Authorization & Permissions

### Role-Based Access Control Flow

```mermaid
flowchart TD
    A[Request with JWT] --> B[Extract User Context]
    B --> C[Load User Roles]
    C --> D[Check Resource Permissions]
    D --> E{Has Permission?}
    
    E -->|Yes| F[Allow Access]
    E -->|No| G[Check Dynamic Rules]
    
    G --> H{Dynamic Rule Match?}
    H -->|Yes| F
    H -->|No| I[Deny Access]
    
    F --> J[Log Access Grant]
    I --> K[Log Access Denial]
    
    C --> L[Role Hierarchy]
    L --> L1[Super Admin]
    L --> L2[Admin]
    L --> L3[Editor]
    L --> L4[Author]
    L --> L5[User]
    
    L1 --> M[All Permissions]
    L2 --> N[Management Permissions]
    L3 --> O[Content Permissions]
    L4 --> P[Own Content Permissions]
    L5 --> Q[Basic Permissions]
```

### Permission Check Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant API as Resource API
    participant Auth as Auth Middleware
    participant RBAC as RBAC Service
    participant Cache as Permission Cache
    participant DB as Database
    
    Client->>API: Request protected resource
    Note over Client,API: Authorization: Bearer <token>
    
    API->>Auth: Validate JWT token
    Auth->>API: User context
    
    API->>RBAC: Check permission
    Note over API,RBAC: checkPermission(user_id, resource, action)
    
    RBAC->>Cache: Check cached permissions
    Cache->>RBAC: Cache result
    
    alt Cache hit
        RBAC->>API: Permission result
    else Cache miss
        RBAC->>DB: Load user roles & permissions
        DB->>RBAC: User permissions
        
        RBAC->>RBAC: Evaluate permission rules
        RBAC->>Cache: Cache permission result
        RBAC->>API: Permission result
    end
    
    alt Permission granted
        API->>Client: 200 Resource data
    else Permission denied
        API->>Client: 403 Forbidden
    end
```

## Security Features

### Rate Limiting Flow

```mermaid
flowchart TD
    A[Incoming Request] --> B[Extract Client Identifier]
    B --> C[Check Rate Limit]
    C --> D{Limit Exceeded?}
    
    D -->|No| E[Process Request]
    D -->|Yes| F[Apply Backoff Strategy]
    
    F --> G{Backoff Type}
    G -->|Fixed| H[Fixed Delay]
    G -->|Exponential| I[Exponential Backoff]
    G -->|Adaptive| J[Adaptive Delay]
    
    H --> K[Return 429 Too Many Requests]
    I --> K
    J --> K
    
    E --> L[Update Rate Counter]
    L --> M[Process Business Logic]
    M --> N[Return Response]
    
    B --> O[Identifier Types]
    O --> O1[IP Address]
    O --> O2[User ID]
    O --> O3[API Key]
    O --> O4[Session ID]
```

### Account Lockout Flow

```mermaid
stateDiagram-v2
    [*] --> Active
    
    Active --> SuspiciousActivity : Failed login attempts
    SuspiciousActivity --> TemporaryLock : Threshold exceeded
    SuspiciousActivity --> Active : Successful login
    
    TemporaryLock --> Active : Lockout period expired
    TemporaryLock --> PermanentLock : Admin action
    TemporaryLock --> Active : Admin unlock
    
    Active --> PermanentLock : Severe security violation
    PermanentLock --> Active : Admin unlock
    
    Active --> Disabled : User deactivation
    Disabled --> Active : User reactivation
    
    note right of SuspiciousActivity
        - Track failed attempts
        - Analyze patterns
        - Send security alerts
    end note
    
    note right of TemporaryLock
        - Duration: 15min - 24hr
        - Progressive lockout
        - Email notification
    end note
```

## API Endpoints với Response Standard

### Authentication Endpoints

#### Register User
```http
POST /api/cms/v1/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "password_confirmation": "SecurePass123!",
  "website_id": 1
}
```

**Response:**
```json
{
  "status": {
    "code": 201,
    "message": "User registered successfully",
    "success": true,
    "path": "/api/cms/v1/auth/register",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "user": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "status": "active",
      "created_at": "2024-01-15T10:30:00Z"
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIs...",
      "refresh_token": "def50200abcd...",
      "token_type": "Bearer",
      "expires_in": 3600
    }
  }
}
```

#### Login User
```http
POST /api/cms/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "remember_me": true,
  "device_info": {
    "device_name": "iPhone 13",
    "device_type": "mobile",
    "os": "iOS 15.0",
    "browser": "Safari"
  }
}
```

#### Multi-Factor Authentication
```http
POST /api/cms/v1/auth/mfa/verify
Content-Type: application/json

{
  "mfa_token": "temp_token_abc123",
  "mfa_code": "123456",
  "remember_device": true
}
```

#### Refresh Token
```http
POST /api/cms/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "def50200abcd..."
}
```

### Profile Management

#### Get Profile
```http
GET /api/cms/v1/auth/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### Update Profile
```http
PUT /api/cms/v1/auth/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "name": "John Smith",
  "bio": "Software developer and blogger",
  "preferences": {
    "language": "en",
    "timezone": "UTC",
    "email_notifications": true
  }
}
```

### Password Management

#### Change Password
```http
POST /api/cms/v1/auth/change-password
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "current_password": "SecurePass123!",
  "new_password": "NewSecurePass456!",
  "new_password_confirmation": "NewSecurePass456!"
}
```

#### Forgot Password
```http
POST /api/cms/v1/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### Reset Password
```http
POST /api/cms/v1/auth/reset-password
Content-Type: application/json

{
  "token": "reset_token_xyz789",
  "password": "NewSecurePass456!",
  "password_confirmation": "NewSecurePass456!"
}
```

### Session Management

#### List Sessions
```http
GET /api/cms/v1/auth/sessions
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Sessions retrieved successfully",
    "success": true,
    "path": "/api/cms/v1/auth/sessions",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": [
    {
      "id": "session_123",
      "device": {
        "name": "iPhone 13",
        "type": "mobile",
        "os": "iOS 15.0",
        "browser": "Safari"
      },
      "location": {
        "country": "Vietnam",
        "city": "Ho Chi Minh City",
        "ip": "***********"
      },
      "last_active": "2024-01-15T10:25:00Z",
      "created_at": "2024-01-15T09:00:00Z",
      "is_current": true
    }
  ]
}
```

#### Terminate Session
```http
DELETE /api/cms/v1/auth/sessions/{session_id}
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

### Security Features

#### Enable MFA
```http
POST /api/cms/v1/auth/mfa/enable
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "method": "totp"
}
```

#### Get Security Log
```http
GET /api/cms/v1/auth/security-log?cursor=abc123&limit=20
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### Report Suspicious Activity
```http
POST /api/cms/v1/auth/security/report
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "event_type": "suspicious_login",
  "description": "Login from unusual location",
  "metadata": {
    "ip": "***********00",
    "user_agent": "Suspicious Bot"
  }
}
```

## Models và Database Schema

### User Model với Security Features
```go
type User struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index;index:idx_website_email,unique" json:"website_id"`
    Email       string    `gorm:"not null;index:idx_website_email,unique" json:"email"`
    Password    string    `gorm:"not null" json:"-"`
    Name        string    `gorm:"size:255;not null" json:"name"`
    
    // Account status
    Status      string    `gorm:"size:20;default:'active'" json:"status"` // active, locked, disabled
    Role        string    `gorm:"size:50;default:'user'" json:"role"`
    
    // Security fields
    FailedLoginAttempts int       `gorm:"default:0" json:"-"`
    LockedUntil        *time.Time `json:"-"`
    LastLoginAt        *time.Time `json:"last_login_at"`
    LastPasswordChange *time.Time `json:"-"`
    
    // MFA
    MFAEnabled    bool   `gorm:"default:false" json:"mfa_enabled"`
    MFASecret     string `gorm:"size:32" json:"-"`
    BackupCodes   JSON   `gorm:"type:json" json:"-"`
    
    // Profile
    Avatar      string `gorm:"size:255" json:"avatar"`
    Bio         string `gorm:"type:text" json:"bio"`
    Preferences JSON   `gorm:"type:json" json:"preferences"`
    
    // Timestamps
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
    
    // Relationships
    Website         Website          `json:"-"`
    RefreshTokens   []RefreshToken   `json:"-"`
    PasswordResets  []PasswordReset  `json:"-"`
    SecurityEvents  []SecurityEvent  `json:"-"`
    OAuthAccounts   []OAuthAccount   `json:"-"`
}
```

### Session Management Models
```go
type RefreshToken struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    WebsiteID uint      `gorm:"not null;index" json:"website_id"`
    Token     string    `gorm:"uniqueIndex;not null;size:255" json:"-"`
    UserID    uint      `gorm:"not null;index" json:"user_id"`
    
    // Device information
    DeviceName   string `gorm:"size:100" json:"device_name"`
    DeviceType   string `gorm:"size:50" json:"device_type"`
    OS           string `gorm:"size:50" json:"os"`
    Browser      string `gorm:"size:50" json:"browser"`
    
    // Location information
    IPAddress    string `gorm:"size:45" json:"ip_address"`
    Country      string `gorm:"size:100" json:"country"`
    City         string `gorm:"size:100" json:"city"`
    
    // Security
    IsRevoked    bool      `gorm:"default:false" json:"is_revoked"`
    LastUsedAt   time.Time `json:"last_used_at"`
    ExpiresAt    time.Time `json:"expires_at"`
    CreatedAt    time.Time `json:"created_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}

type SecurityEvent struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    UserID      uint      `gorm:"not null;index" json:"user_id"`
    
    EventType   string    `gorm:"size:50;not null" json:"event_type"`
    Description string    `gorm:"size:255" json:"description"`
    
    // Context
    IPAddress   string    `gorm:"size:45" json:"ip_address"`
    UserAgent   string    `gorm:"size:500" json:"user_agent"`
    Location    JSON      `gorm:"type:json" json:"location"`
    
    // Risk assessment
    RiskScore   int       `gorm:"default:0" json:"risk_score"`
    Status      string    `gorm:"size:20;default:'normal'" json:"status"`
    
    // Additional data
    Metadata    JSON      `gorm:"type:json" json:"metadata"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    User User `json:"user,omitempty"`
}
```

## Security Implementation

### Password Security
```go
type PasswordManager struct {
    minLength    int
    requireUpper bool
    requireLower bool
    requireNumber bool
    requireSpecial bool
    historyLimit int
}

func (pm *PasswordManager) ValidatePassword(password string) error {
    if len(password) < pm.minLength {
        return errors.New("password too short")
    }
    
    var hasUpper, hasLower, hasNumber, hasSpecial bool
    
    for _, char := range password {
        switch {
        case unicode.IsUpper(char):
            hasUpper = true
        case unicode.IsLower(char):
            hasLower = true
        case unicode.IsNumber(char):
            hasNumber = true
        case unicode.IsPunct(char) || unicode.IsSymbol(char):
            hasSpecial = true
        }
    }
    
    if pm.requireUpper && !hasUpper {
        return errors.New("password must contain uppercase letter")
    }
    // ... other validations
    
    return nil
}

func (pm *PasswordManager) HashPassword(password string) (string, error) {
    cost := 12 // bcrypt cost
    hash, err := bcrypt.GenerateFromPassword([]byte(password), cost)
    return string(hash), err
}
```

### Rate Limiting Implementation
```go
type RateLimiter struct {
    redis  *redis.Client
    limits map[string]RateLimit
}

type RateLimit struct {
    Requests int           `json:"requests"`
    Window   time.Duration `json:"window"`
    Burst    int           `json:"burst"`
}

func (rl *RateLimiter) CheckLimit(key string, limitType string) (bool, error) {
    limit, exists := rl.limits[limitType]
    if !exists {
        return true, nil // No limit configured
    }
    
    // Sliding window implementation
    now := time.Now()
    window := now.Add(-limit.Window)
    
    pipe := rl.redis.Pipeline()
    
    // Remove old entries
    pipe.ZRemRangeByScore(key, "0", fmt.Sprintf("%d", window.Unix()))
    
    // Count current requests
    pipe.ZCard(key)
    
    // Add current request
    pipe.ZAdd(key, &redis.Z{
        Score:  float64(now.Unix()),
        Member: fmt.Sprintf("%d", now.UnixNano()),
    })
    
    // Set expiration
    pipe.Expire(key, limit.Window+time.Minute)
    
    results, err := pipe.Exec()
    if err != nil {
        return false, err
    }
    
    count := results[1].(*redis.IntCmd).Val()
    return count < int64(limit.Requests), nil
}
```

## Testing Strategies

### Unit Tests
```go
func TestAuthService_Login_Success(t *testing.T) {
    // Setup
    mockRepo := new(MockAuthRepository)
    jwtUtil := NewJWTUtil("secret", time.Hour, time.Hour*24)
    service := NewAuthService(mockRepo, jwtUtil)
    
    user := &User{
        ID:       1,
        Email:    "<EMAIL>",
        Password: hashPassword("password123"),
        Status:   "active",
    }
    
    mockRepo.On("GetUserByEmail", "<EMAIL>").Return(user, nil)
    mockRepo.On("UpdateUser", mock.AnythingOfType("*User")).Return(nil)
    
    // Execute
    response, err := service.Login(&LoginRequest{
        Email:    "<EMAIL>",
        Password: "password123",
    })
    
    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, response)
    assert.Equal(t, user.Email, response.User.Email)
    assert.NotEmpty(t, response.AccessToken)
    
    mockRepo.AssertExpectations(t)
}
```

### Integration Tests
```go
func TestAuth_LoginFlow_Integration(t *testing.T) {
    // Setup test server
    server := setupTestServer()
    defer server.Close()
    
    // Create test user
    user := createTestUser(t, "<EMAIL>", "password123")
    
    // Test login
    loginReq := LoginRequest{
        Email:    "<EMAIL>",
        Password: "password123",
    }
    
    resp, err := http.Post(
        server.URL+"/auth/login",
        "application/json",
        toJSONReader(loginReq),
    )
    
    assert.NoError(t, err)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
    
    var loginResp AuthResponse
    json.NewDecoder(resp.Body).Decode(&loginResp)
    
    assert.Equal(t, user.Email, loginResp.User.Email)
    assert.NotEmpty(t, loginResp.AccessToken)
    
    // Test authenticated request
    req, _ := http.NewRequest("GET", server.URL+"/auth/profile", nil)
    req.Header.Set("Authorization", "Bearer "+loginResp.AccessToken)
    
    resp, err = http.DefaultClient.Do(req)
    assert.NoError(t, err)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
}
```

## Configuration

### Auth Configuration
```yaml
auth:
  jwt:
    secret: "${JWT_SECRET}"
    access_token_ttl: "1h"
    refresh_token_ttl: "24h"
    
  password:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special: true
    history_limit: 5
    
  security:
    max_failed_attempts: 5
    lockout_duration: "15m"
    progressive_lockout: true
    
  rate_limiting:
    login:
      requests: 5
      window: "5m"
    register:
      requests: 3
      window: "10m"
    password_reset:
      requests: 3
      window: "1h"
      
  mfa:
    enabled: true
    issuer: "Blog API v3"
    backup_codes_count: 8
    
  oauth:
    google:
      client_id: "${GOOGLE_CLIENT_ID}"
      client_secret: "${GOOGLE_CLIENT_SECRET}"
      redirect_url: "${BASE_URL}/auth/oauth/google/callback"
    github:
      client_id: "${GITHUB_CLIENT_ID}"
      client_secret: "${GITHUB_CLIENT_SECRET}"
```

## Tenant Isolation

### Multi-tenancy Implementation
```go
type AuthRepository struct {
    db *gorm.DB
}

// GetUserByEmail với tenant isolation
func (r *AuthRepository) GetUserByEmail(websiteID uint, email string) (*User, error) {
    var user User
    err := r.db.Where("website_id = ? AND email = ?", websiteID, email).First(&user).Error
    return &user, err
}

// CreateUser với tenant validation
func (r *AuthRepository) CreateUser(user *User) error {
    // Validate website exists
    var website Website
    if err := r.db.First(&website, user.WebsiteID).Error; err != nil {
        return errors.New("invalid website_id")
    }
    
    // Check website user limits
    var userCount int64
    r.db.Model(&User{}).Where("website_id = ?", user.WebsiteID).Count(&userCount)
    if userCount >= website.MaxUsers {
        return errors.New("website user limit exceeded")
    }
    
    return r.db.Create(user).Error
}

// GetUserSessions với tenant filtering
func (r *AuthRepository) GetUserSessions(websiteID, userID uint) ([]RefreshToken, error) {
    var tokens []RefreshToken
    err := r.db.Where("website_id = ? AND user_id = ?", websiteID, userID).
        Order("last_used_at DESC").
        Find(&tokens).Error
    return tokens, err
}
```

### Cache Key Patterns
```go
// Cache keys với website_id prefix
const (
    UserCacheKey     = "website:%d:user:%d"                // website:1:user:123
    SessionCacheKey  = "website:%d:session:%s"            // website:1:session:abc123
    RateLimitKey     = "website:%d:ratelimit:%s:%s"       // website:1:ratelimit:login:<EMAIL>
    MFASecretKey     = "website:%d:mfa:%d"                // website:1:mfa:123
    PasswordResetKey = "website:%d:pwreset:%s"            // website:1:pwreset:token123
)

// Helper functions
func GetUserCacheKey(websiteID, userID uint) string {
    return fmt.Sprintf(UserCacheKey, websiteID, userID)
}

func GetSessionCacheKey(websiteID uint, sessionID string) string {
    return fmt.Sprintf(SessionCacheKey, websiteID, sessionID)
}
```

### JWT Token với Tenant Context
```go
type TokenClaims struct {
    jwt.StandardClaims
    UserID    uint   `json:"user_id"`
    WebsiteID uint   `json:"website_id"`
    Email     string `json:"email"`
    Role      string `json:"role"`
}

func GenerateToken(user *User) (string, error) {
    claims := TokenClaims{
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: time.Now().Add(time.Hour).Unix(),
            IssuedAt:  time.Now().Unix(),
        },
        UserID:    user.ID,
        WebsiteID: user.WebsiteID,
        Email:     user.Email,
        Role:      user.Role,
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(jwtSecret))
}
```

### API Middleware với Tenant Validation
```go
func TenantAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Extract token claims
        claims, exists := c.Get("claims")
        if !exists {
            c.JSON(401, gin.H{"error": "unauthorized"})
            c.Abort()
            return
        }
        
        tokenClaims := claims.(*TokenClaims)
        
        // Validate website_id from header matches token
        websiteID := c.GetHeader("X-Website-ID")
        if websiteID == "" {
            c.JSON(400, gin.H{"error": "missing website_id"})
            c.Abort()
            return
        }
        
        requestWebsiteID, _ := strconv.ParseUint(websiteID, 10, 32)
        if uint(requestWebsiteID) != tokenClaims.WebsiteID {
            c.JSON(403, gin.H{"error": "website_id mismatch"})
            c.Abort()
            return
        }
        
        // Set tenant context
        c.Set("website_id", tokenClaims.WebsiteID)
        c.Set("user_id", tokenClaims.UserID)
        c.Next()
    }
}
```

## Best Practices

### Security Best Practices
- **Password Hashing**: Sử dụng bcrypt với cost factor 12+
- **JWT Security**: Short-lived access tokens (15-60 phút)
- **Rate Limiting**: Implement rate limiting cho tất cả auth endpoints
- **Account Lockout**: Progressive lockout sau failed attempts
- **Audit Logging**: Log tất cả security events
- **MFA Support**: Encourage multi-factor authentication
- **Tenant Isolation**: Validate website_id trong mọi query

### Performance Best Practices
- **Token Caching**: Cache JWT validation results với website_id prefix
- **Session Storage**: Use Redis cho session management với tenant keys
- **Permission Caching**: Cache user permissions theo website
- **Connection Pooling**: Optimize database connections

### Monitoring & Alerting
- **Failed Login Monitoring**: Alert on suspicious patterns per website
- **Account Lockout Alerts**: Notify on account lockouts per tenant
- **Token Abuse Detection**: Monitor for token replay attacks
- **Geographic Anomalies**: Detect logins from unusual locations
- **Cross-tenant Access**: Alert on unauthorized tenant access attempts

## Tài liệu liên quan

- [RBAC Module](./rbac.md)
- [Response Standard](../api/response-standard.md)
- [Security Best Practices](../best-practices/security.md)
- [API Documentation](../api/overview.md)
- [Testing Guidelines](../development/testing.md)