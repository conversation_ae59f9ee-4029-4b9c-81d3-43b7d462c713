# Blog Submission Flow - <PERSON><PERSON><PERSON> gửi bài viết

## Tổng quan

Tài liệu này mô tả chi tiết luồng xử lý khi người dùng gửi bài viết blog, từ việc tạo draft, review, publish đến tương tác với các module kh<PERSON><PERSON> trong hệ thống.

## <PERSON><PERSON>c bước trong quy trình

### 1. User tạo Draft Blog Post

```mermaid
sequenceDiagram
    participant User as User/Author
    participant BlogAPI as Blog API
    participant Auth as Auth Module
    participant Media as Media Module
    participant DB as Database
    participant Cache as Redis Cache
    
    User->>BlogAPI: POST /api/v1/blog/posts/draft
    Note over User,BlogAPI: {title, content, category_id, tags[], featured_image, website_id}
    
    BlogAPI->>Auth: Verify user token + website access
    Auth->>BlogAPI: User authenticated + permissions for website
    
    alt User not authorized to create posts
        BlogAPI->>User: 403 Forbidden
    end
    
    BlogAPI->>DB: Create draft post with website_id
    DB->>BlogAPI: Draft created with ID
    
    alt Has featured image
        BlogAPI->>Media: Process & store image
        Media->>BlogAPI: Image URL
        BlogAPI->>DB: Update post with image URL
    end
    
    BlogAPI->>Cache: Cache draft with website_id prefix
    BlogAPI->>User: 201 Draft created
    Note over User,BlogAPI: {post_id, status: "draft", edit_url}
```

### 2. Auto-save và Real-time Collaboration

```mermaid
flowchart TD
    subgraph "Auto-save System"
        A[User typing] --> B{Every 30s or 100 chars}
        B -->|Yes| C[Send auto-save request]
        C --> D[Update draft in DB]
        D --> E[Update cache]
        E --> F[Emit save event]
    end
    
    subgraph "Real-time Collaboration"
        F --> G[Socket Module]
        G --> H{Other editors online?}
        H -->|Yes| I[Broadcast changes]
        I --> J[Update other editors' view]
        H -->|No| K[Log activity only]
    end
    
    subgraph "Conflict Resolution"
        J --> L{Conflict detected?}
        L -->|Yes| M[Show merge dialog]
        L -->|No| N[Apply changes smoothly]
        M --> O[User resolves conflict]
    end
```

### 3. Submit for Review Flow

```mermaid
sequenceDiagram
    participant Author as Author
    participant BlogAPI as Blog API
    participant Validator as Content Validator
    participant SEO as SEO Module
    participant Review as Review System
    participant Notification as Notification Module
    participant Queue as Message Queue
    
    Author->>BlogAPI: POST /api/v1/blog/posts/{id}/submit
    Note over Author,BlogAPI: {message?, reviewers[]?}
    
    BlogAPI->>Validator: Validate content completeness
    Validator->>BlogAPI: Validation result
    
    alt Content incomplete
        BlogAPI->>Author: 422 Missing required fields
        Note over BlogAPI,Author: {missing_fields: [...]}
    end
    
    BlogAPI->>SEO: Check SEO requirements
    SEO->>BlogAPI: SEO score & suggestions
    
    alt SEO score too low
        BlogAPI->>Author: 422 SEO improvements needed
        Note over BlogAPI,Author: {seo_suggestions: [...]}
    end
    
    BlogAPI->>Review: Create review request
    Review->>BlogAPI: Review ID
    
    BlogAPI->>Queue: Publish "post.submitted_for_review"
    
    Queue->>Notification: Send notifications
    Notification->>Notification: Email to editors
    Notification->>Notification: In-app notification
    Notification->>Notification: Push notification
    
    BlogAPI->>Author: 200 Submitted for review
    Note over BlogAPI,Author: {review_id, estimated_time}
```

### 4. Editorial Review Process

```mermaid
flowchart TD
    subgraph "Review Assignment"
        A[Post submitted] --> B{Auto-assign?}
        B -->|Yes| C[Find available editor]
        B -->|No| D[Wait for manual claim]
        C --> E[Assign to editor]
        D --> F[Editor claims review]
    end
    
    subgraph "Review Process"
        E --> G[Editor reviews post]
        F --> G
        G --> H{Decision}
        H -->|Approve| I[Mark as approved]
        H -->|Reject| J[Add feedback]
        H -->|Request changes| K[List required changes]
    end
    
    subgraph "Author Actions"
        J --> L[Author sees feedback]
        K --> M[Author makes changes]
        M --> N[Resubmit for review]
        N --> G
    end
    
    subgraph "Publishing"
        I --> O{Publish immediately?}
        O -->|Yes| P[Publish post]
        O -->|No| Q[Schedule publication]
    end
```

### 5. Publishing Flow với Module Integration

```mermaid
sequenceDiagram
    participant Editor as Editor/Admin
    participant BlogAPI as Blog API
    participant DB as Database
    participant Queue as Message Queue
    participant Cache as Cache
    participant CDN as CDN
    
    Editor->>BlogAPI: POST /api/v1/blog/posts/{id}/publish
    Note over Editor,BlogAPI: {publish_at?, notification_channels[]?}
    
    BlogAPI->>DB: Update post status to "published"
    BlogAPI->>DB: Set published_at timestamp
    
    BlogAPI->>Cache: Invalidate post cache
    BlogAPI->>Cache: Invalidate category cache
    BlogAPI->>Cache: Invalidate tag cache
    BlogAPI->>Cache: Invalidate author posts cache
    
    BlogAPI->>CDN: Purge related URLs
    
    BlogAPI->>Queue: Publish "post.published" event
    
    BlogAPI->>Editor: 200 Post published
    Note over BlogAPI,Editor: {post_id, published_at, public_url}
```

### 6. Post-Publishing Module Interactions

```mermaid
flowchart TD
    subgraph "Event: post.published"
        A[Message Queue] --> B{Event Consumers}
    end
    
    subgraph "Notification Module"
        B --> C[Notification Service]
        C --> C1[Email subscribers]
        C --> C2[Push to app users]
        C --> C3[SMS notifications]
        C --> C4[Webhook calls]
    end
    
    subgraph "SEO Module"
        B --> D[SEO Service]
        D --> D1[Generate sitemap]
        D --> D2[Submit to search engines]
        D --> D3[Update meta tags]
        D --> D4[Create structured data]
    end
    
    subgraph "Social Media Module"
        B --> E[Social Service]
        E --> E1[Auto-post to Facebook]
        E --> E2[Tweet on Twitter]
        E --> E3[Share on LinkedIn]
        E --> E4[Post to Instagram]
    end
    
    subgraph "Analytics Module"
        B --> F[Analytics Service]
        F --> F1[Track publication]
        F --> F2[Author analytics]
        F --> F3[Category performance]
        F --> F4[Initialize view tracking]
    end
    
    subgraph "Socket Module"
        B --> G[Real-time Service]
        G --> G1[Notify online users]
        G --> G2[Update live feeds]
        G --> G3[Refresh home page]
    end
    
    subgraph "Search Module"
        B --> H[Search Service]
        H --> H1[Index post content]
        H --> H2[Update search suggestions]
        H --> H3[Build related posts]
    end
    
    subgraph "Payment Module"
        B --> I[Payment Service]
        I --> I1[Check if premium content]
        I --> I2[Update author earnings]
        I --> I3[Process revenue share]
    end
    
    subgraph "Media Module"
        B --> J[Media Service]
        J --> J1[Optimize images]
        J --> J2[Generate thumbnails]
        J --> J3[Create social media images]
    end
```

## Chi tiết các validation rules

### Content Validation

```mermaid
flowchart LR
    subgraph "Basic Validations"
        A[Title] --> A1[Length: 10-200 chars]
        A --> A2[No special chars]
        A --> A3[Unique in tenant]
        
        B[Content] --> B1[Min 300 words]
        B --> B2[No prohibited words]
        B --> B3[Valid HTML/Markdown]
        
        C[Category] --> C1[Must exist]
        C --> C2[User has access]
        C --> C3[Active category]
        
        D[Tags] --> D1[Max 10 tags]
        D --> D2[Valid tag format]
        D --> D3[Create if not exist]
    end
```

### SEO Validation

```mermaid
flowchart TD
    subgraph "SEO Requirements"
        A[Meta Title] --> A1{Exists?}
        A1 -->|No| A2[Use post title]
        A1 -->|Yes| A3[Length: 50-60 chars]
        
        B[Meta Description] --> B1{Exists?}
        B1 -->|No| B2[Auto-generate from content]
        B1 -->|Yes| B3[Length: 150-160 chars]
        
        C[URL Slug] --> C1{Custom?}
        C1 -->|No| C2[Generate from title]
        C1 -->|Yes| C3[Validate format]
        C3 --> C4[Check uniqueness]
        
        D[Keywords] --> D1[Density check]
        D --> D2[Relevance score]
        D --> D3[Competition analysis]
    end
```

## State Transitions

```mermaid
stateDiagram-v2
    [*] --> Draft: Create new post
    Draft --> Draft: Auto-save
    Draft --> Submitted: Submit for review
    Submitted --> UnderReview: Editor picks up
    UnderReview --> Rejected: Editor rejects
    UnderReview --> ChangesRequested: Need changes
    UnderReview --> Approved: Editor approves
    Rejected --> Draft: Author revises
    ChangesRequested --> Draft: Author edits
    Approved --> Published: Publish immediately
    Approved --> Scheduled: Schedule for later
    Scheduled --> Published: Publish time reached
    Published --> Updated: Edit published post
    Updated --> Published: Save changes
    Published --> Archived: Archive post
    Archived --> Published: Restore post
    Published --> Deleted: Soft delete
    Deleted --> [*]
```

## Error Handling

### Common Errors và Cách xử lý

| Error Code | Error Type | Description | User Action |
|------------|------------|-------------|-------------|
| 400 | Bad Request | Invalid input format | Check request format |
| 401 | Unauthorized | Missing/invalid token | Login again |
| 403 | Forbidden | No permission to create | Check user role |
| 409 | Conflict | Duplicate title/slug | Change title |
| 413 | Payload Too Large | Content/image too large | Reduce size |
| 422 | Unprocessable | Validation failed | Fix validation errors |
| 429 | Rate Limited | Too many requests | Wait and retry |
| 500 | Server Error | Internal error | Contact support |

## Performance Optimizations

### 1. Draft Auto-save

```mermaid
flowchart LR
    A[User Input] --> B{Debounce 2s}
    B --> C[Check changes]
    C --> D{Changed?}
    D -->|Yes| E[Save to local storage]
    E --> F{Network available?}
    F -->|Yes| G[Sync to server]
    F -->|No| H[Queue for later]
    D -->|No| I[Skip save]
```

### 2. Image Optimization Pipeline

```mermaid
flowchart TD
    A[Upload Image] --> B[Validate format/size]
    B --> C{Valid?}
    C -->|No| D[Return error]
    C -->|Yes| E[Create variants]
    E --> E1[Thumbnail 150x150]
    E --> E2[Small 400x300]
    E --> E3[Medium 800x600]
    E --> E4[Large 1200x900]
    E --> E5[Original]
    E1 --> F[Optimize & compress]
    E2 --> F
    E3 --> F
    E4 --> F
    E5 --> F
    F --> G[Upload to CDN]
    G --> H[Return URLs]
```

### 3. Content Delivery Optimization

```mermaid
flowchart LR
    subgraph "Cache Layers"
        A[Database] --> B[Redis Cache]
        B --> C[Application Cache]
        C --> D[CDN Cache]
        D --> E[Browser Cache]
    end
    
    subgraph "Cache Invalidation"
        F[Post Update] --> G[Clear Redis]
        G --> H[Clear App Cache]
        H --> I[Purge CDN]
        I --> J[Force browser refresh]
    end
```

## API Endpoints

### Draft Management
- `POST /api/v1/blog/posts/draft` - Create draft (website_id scoped)
- `PUT /api/v1/blog/posts/{id}/draft` - Update draft (website_id scoped)
- `POST /api/v1/blog/posts/{id}/auto-save` - Auto-save draft (website_id scoped)
- `DELETE /api/v1/blog/posts/{id}/draft` - Delete draft (website_id scoped)

### Submission & Review
- `POST /api/v1/blog/posts/{id}/submit` - Submit for review (website_id scoped)
- `GET /api/v1/blog/posts/{id}/review-status` - Check review status (website_id scoped)
- `POST /api/v1/blog/posts/{id}/review` - Add review feedback (website_id scoped)
- `PUT /api/v1/blog/posts/{id}/review/approve` - Approve post (website_id scoped)
- `PUT /api/v1/blog/posts/{id}/review/reject` - Reject post (website_id scoped)

### Publishing
- `POST /api/v1/blog/posts/{id}/publish` - Publish immediately (website_id scoped)
- `POST /api/v1/blog/posts/{id}/schedule` - Schedule publication (website_id scoped)
- `PUT /api/v1/blog/posts/{id}/unpublish` - Unpublish post (website_id scoped)
- `PUT /api/v1/blog/posts/{id}/archive` - Archive post (website_id scoped)

## Webhook Events

Hệ thống sẽ gửi webhooks cho các events sau:

```json
{
  "event": "blog.post.created",
  "data": {
    "post_id": "123",
    "author_id": "456",
    "status": "draft",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### Supported Events
- `blog.post.created` - Draft created
- `blog.post.submitted` - Submitted for review
- `blog.post.reviewed` - Review completed
- `blog.post.published` - Post published
- `blog.post.updated` - Post updated
- `blog.post.deleted` - Post deleted
- `blog.post.viewed` - Post viewed (batched)
- `blog.post.commented` - New comment
- `blog.post.shared` - Post shared

## Security Considerations

### 1. Authentication & Authorization

```mermaid
flowchart TD
    A[Request] --> B{Has token?}
    B -->|No| C[401 Unauthorized]
    B -->|Yes| D{Valid token?}
    D -->|No| E[401 Invalid token]
    D -->|Yes| F{Has permission?}
    F -->|No| G[403 Forbidden]
    F -->|Yes| H[Process request]
    
    F --> I{Check conditions}
    I --> I1[User role]
    I --> I2[Tenant limits]
    I --> I3[Resource ownership]
    I --> I4[Feature flags]
```

### 2. Content Security
- XSS prevention: Sanitize HTML content
- SQL injection: Use parameterized queries
- File upload: Validate file types and sizes
- Rate limiting: Prevent spam and DDoS
- CORS: Restrict cross-origin requests

## Multi-Tenancy Isolation

### Repository Pattern với Website ID

```go
type BlogRepository interface {
    GetPostsByWebsiteID(websiteID int64, status string) ([]*Post, error)
    CreatePost(post *Post) error
    UpdatePost(post *Post) error
    DeletePost(postID int64, websiteID int64) error
}

type blogRepository struct {
    db *gorm.DB
}

func (r *blogRepository) GetPostsByWebsiteID(websiteID int64, status string) ([]*Post, error) {
    var posts []*Post
    query := r.db.Where("website_id = ?", websiteID)
    if status != "" {
        query = query.Where("status = ?", status)
    }
    return posts, query.Find(&posts).Error
}

func (r *blogRepository) CreatePost(post *Post) error {
    return r.db.Create(post).Error
}

func (r *blogRepository) UpdatePost(post *Post) error {
    return r.db.Where("id = ? AND website_id = ?", post.ID, post.WebsiteID).Updates(post).Error
}

func (r *blogRepository) DeletePost(postID int64, websiteID int64) error {
    return r.db.Where("id = ? AND website_id = ?", postID, websiteID).Delete(&Post{}).Error
}
```

### Cache Keys với Website ID

```go
func (s *BlogService) getCacheKey(websiteID int64, suffix string) string {
    return fmt.Sprintf("blog:website:%d:%s", websiteID, suffix)
}

// Cache post data
func (s *BlogService) cachePost(websiteID int64, post *Post) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("post:%d", post.ID))
    s.cache.Set(key, post, 30*time.Minute)
}

// Cache draft data
func (s *BlogService) cacheDraft(websiteID int64, userID int64, post *Post) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("draft:%d:%d", userID, post.ID))
    s.cache.Set(key, post, 1*time.Hour)
}

// Cache user posts
func (s *BlogService) cacheUserPosts(websiteID int64, userID int64, posts []*Post) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("user_posts:%d", userID))
    s.cache.Set(key, posts, 15*time.Minute)
}
```

### Service Layer với Website Context

```go
type BlogService struct {
    repo  BlogRepository
    cache CacheService
}

func (s *BlogService) CreateDraftForWebsite(websiteID int64, userID int64, req *CreatePostRequest) (*Post, error) {
    // Validate user has access to website
    if !s.userHasWebsiteAccess(userID, websiteID) {
        return nil, ErrInsufficientPermissions
    }
    
    post := &Post{
        WebsiteID:   websiteID,
        AuthorID:    userID,
        Title:       req.Title,
        Content:     req.Content,
        Status:      "draft",
        CreatedAt:   time.Now(),
        UpdatedAt:   time.Now(),
    }
    
    if err := s.repo.CreatePost(post); err != nil {
        return nil, err
    }
    
    // Cache the draft
    s.cacheDraft(websiteID, userID, post)
    return post, nil
}

func (s *BlogService) GetPostForWebsite(websiteID int64, postID int64) (*Post, error) {
    // Check cache first
    cacheKey := s.getCacheKey(websiteID, fmt.Sprintf("post:%d", postID))
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*Post), nil
    }
    
    // Fetch from database with website_id filter
    post, err := s.repo.GetPostByIDAndWebsiteID(postID, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cachePost(websiteID, post)
    return post, nil
}
```

### Middleware for Website Context

```go
func BlogWebsiteContextMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := c.GetHeader("X-Website-ID")
        if websiteID == "" {
            c.JSON(400, gin.H{"error": "Website ID is required"})
            c.Abort()
            return
        }
        
        id, err := strconv.ParseInt(websiteID, 10, 64)
        if err != nil {
            c.JSON(400, gin.H{"error": "Invalid website ID"})
            c.Abort()
            return
        }
        
        // Validate user has access to this website
        userID := c.GetInt64("user_id")
        if !validateUserWebsiteAccess(userID, id) {
            c.JSON(403, gin.H{"error": "Insufficient permissions for this website"})
            c.Abort()
            return
        }
        
        c.Set("website_id", id)
        c.Next()
    }
}
```

### Response Context

```go
type BlogResponse struct {
    Status  Status      `json:"status"`
    Data    interface{} `json:"data"`
    Meta    *Meta       `json:"meta,omitempty"`
    Website *WebsiteContext `json:"website,omitempty"`
}

type WebsiteContext struct {
    ID     int64  `json:"id"`
    Domain string `json:"domain"`
    Name   string `json:"name"`
}

func (s *BlogService) AddWebsiteContext(response *BlogResponse, websiteID int64) {
    if website, err := s.GetWebsiteByID(websiteID); err == nil {
        response.Website = &WebsiteContext{
            ID:     website.ID,
            Domain: website.Domain,
            Name:   website.Name,
        }
    }
}
```

## Best Practices

### For Authors
1. **Save drafts frequently** - Use auto-save feature
2. **Add meta information** - Improve SEO
3. **Use categories and tags** - Better organization
4. **Preview before submit** - Check formatting
5. **Respond to feedback quickly** - Faster publication

### For Editors
1. **Set clear guidelines** - Consistent quality
2. **Provide constructive feedback** - Help authors improve
3. **Use templates** - Standardize reviews
4. **Track metrics** - Monitor performance
5. **Communicate delays** - Manage expectations

### For Developers
1. **Handle errors gracefully** - User-friendly messages
2. **Implement retries** - For transient failures
3. **Log important events** - Debugging and audit
4. **Monitor performance** - Identify bottlenecks
5. **Test edge cases** - Ensure reliability