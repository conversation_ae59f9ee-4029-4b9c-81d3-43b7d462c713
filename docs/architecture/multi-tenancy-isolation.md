# Multi-Tenancy Data Isolation Strategy

## Tổng quan

Tài liệu này phân tích chiến lược isolation data theo website_id (tenant isolation) cho hệ thống Blog API v3.

## Khuyến nghị: CÓ nên isolate data theo website_id

### Lý do chính

1. **Data Security** - Tránh data leakage giữa các tenants
2. **Performance** - Query chỉ trong phạm vi tenant
3. **Scalability** - D<PERSON> dàng sharding theo tenant
4. **Compliance** - Đá<PERSON> ứng yêu cầu GDPR, data residency
5. **Customization** - Mỗi tenant có thể có cấu hình riêng

## Chiến lược Implementation

### 1. Database-level Isolation

```mermaid
flowchart TD
    A[Request] --> B{Get website_id from context}
    B --> C[Query with website_id filter]
    
    subgraph "Database Tables"
        D[users] --> D1[website_id column]
        E[blog_posts] --> E1[website_id column]
        F[rbac_roles] --> F1[website_id column]
        G[rbac_permissions] --> G1[website_id column]
        H[media_files] --> H1[website_id column]
        I[notifications] --> I1[website_id column]
    end
    
    C --> D
    C --> E
    C --> F
    C --> G
    C --> H
    C --> I
```

### 2. Application-level Isolation

```go
// Middleware to inject website_id into context
func TenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := extractWebsiteID(c)
        c.Set("website_id", websiteID)
        c.Next()
    }
}

// Repository pattern with automatic tenant filtering
type BlogRepository struct {
    db *gorm.DB
}

func (r *BlogRepository) FindByID(ctx context.Context, id uint) (*BlogPost, error) {
    websiteID := ctx.Value("website_id").(uint)
    
    var post BlogPost
    err := r.db.Where("id = ? AND website_id = ?", id, websiteID).First(&post).Error
    return &post, err
}

// Global scope for automatic filtering
func RegisterTenantScope(db *gorm.DB) {
    db.Scopes(func(db *gorm.DB) *gorm.DB {
        websiteID := getCurrentWebsiteID()
        if websiteID > 0 {
            return db.Where("website_id = ?", websiteID)
        }
        return db
    })
}
```

### 3. RBAC Isolation Strategy

```mermaid
flowchart TD
    subgraph "RBAC per Website"
        A[Website 1] --> A1[Admin Role]
        A --> A2[Editor Role]
        A --> A3[Author Role]
        A --> A4[Custom Roles]
        
        B[Website 2] --> B1[Manager Role]
        B --> B2[Writer Role]
        B --> B3[Viewer Role]
        B --> B4[Custom Roles]
    end
    
    subgraph "Permission Inheritance"
        C[System Default Permissions]
        C --> A1
        C --> B1
        
        D[Tenant Custom Permissions]
        D --> A4
        D --> B4
    end
```

### 4. Blog Content Isolation

```mermaid
erDiagram
    websites ||--o{ blog_posts : has
    websites ||--o{ blog_categories : has
    websites ||--o{ blog_tags : has
    websites ||--o{ blog_authors : has
    
    blog_posts {
        int id PK
        int website_id FK
        string title
        text content
        int author_id FK
        int category_id FK
    }
    
    blog_categories {
        int id PK
        int website_id FK
        string name
        int parent_id
        int lft
        int rgt
    }
    
    blog_tags {
        int id PK
        int website_id FK
        string name
        string slug
    }
```

## Implementation Patterns

### 1. Repository Pattern với Tenant Filtering

```go
type TenantAwareRepository struct {
    db *gorm.DB
}

// Base query với automatic tenant filtering
func (r *TenantAwareRepository) baseQuery(websiteID uint) *gorm.DB {
    return r.db.Where("website_id = ?", websiteID)
}

// Blog Repository
type BlogRepository struct {
    TenantAwareRepository
}

func (r *BlogRepository) GetPosts(websiteID uint, limit int) ([]BlogPost, error) {
    var posts []BlogPost
    err := r.baseQuery(websiteID).
        Limit(limit).
        Order("created_at DESC").
        Find(&posts).Error
    return posts, err
}

// RBAC Repository
type RBACRepository struct {
    TenantAwareRepository
}

func (r *RBACRepository) GetRoles(websiteID uint) ([]Role, error) {
    var roles []Role
    err := r.baseQuery(websiteID).
        Preload("Permissions").
        Find(&roles).Error
    return roles, err
}
```

### 2. Cache Strategy với Tenant Isolation

```mermaid
flowchart LR
    subgraph "Cache Keys with website_id"
        A[website:1:posts:page:1]
        B[website:1:roles:all]
        C[website:1:users:online]
        
        D[website:2:posts:page:1]
        E[website:2:roles:all]
        F[website:2:users:online]
    end
    
    subgraph "Cache Invalidation"
        G[Update Post] --> H[Invalidate website:1:posts:*]
        I[Update Role] --> J[Invalidate website:1:roles:*]
    end
```

### 3. Search Index Isolation

```mermaid
flowchart TD
    subgraph "Elasticsearch Indices"
        A[website_1_posts]
        B[website_1_users]
        C[website_1_media]
        
        D[website_2_posts]
        E[website_2_users]
        F[website_2_media]
    end
    
    subgraph "Search Query"
        G[Search Request] --> H{Get website_id}
        H --> I[Route to correct index]
        I --> A
        I --> D
    end
```

## Ưu điểm của Tenant Isolation

### 1. Security
- **Complete data isolation**: Không có khả năng truy cập cross-tenant
- **Audit trail**: Dễ dàng track access theo tenant
- **Compliance**: Đáp ứng các yêu cầu về data privacy

### 2. Performance
- **Smaller datasets**: Query chỉ trong data của tenant
- **Better indexing**: Index nhỏ hơn, hiệu quả hơn
- **Cache efficiency**: Cache riêng biệt cho mỗi tenant

### 3. Scalability
- **Horizontal scaling**: Dễ dàng shard theo tenant
- **Resource allocation**: Có thể allocate resource theo tenant
- **Independent scaling**: Scale riêng cho tenant lớn

### 4. Business Benefits
- **Customization**: Mỗi tenant có thể customize features
- **White-labeling**: Hoàn toàn độc lập về branding
- **Pricing tiers**: Dễ dàng implement các gói khác nhau

## Nhược điểm và Cách khắc phục

### 1. Code Complexity
**Problem**: Phải luôn nhớ filter theo website_id
**Solution**: 
- Sử dụng middleware và global scopes
- Repository pattern với base filtering
- Automated testing cho tenant isolation

### 2. Cross-tenant Features
**Problem**: Khó implement features cần data từ nhiều tenants
**Solution**:
- Separate admin portal với quyền cross-tenant
- API endpoints đặc biệt cho super admin
- Aggregation service riêng

### 3. Data Duplication
**Problem**: Duplicate data như roles, permissions mặc định
**Solution**:
- Template system cho default data
- Copy-on-write cho customization
- Shared reference data với tenant overrides

## Migration Strategy

### Phase 1: Add website_id columns
```sql
-- Add website_id to all tenant-specific tables
ALTER TABLE users ADD COLUMN website_id INT NOT NULL;
ALTER TABLE blog_posts ADD COLUMN website_id INT NOT NULL;
ALTER TABLE rbac_roles ADD COLUMN website_id INT NOT NULL;
ALTER TABLE rbac_permissions ADD COLUMN website_id INT NOT NULL;

-- Add indexes
CREATE INDEX idx_users_website_id ON users(website_id);
CREATE INDEX idx_blog_posts_website_id ON blog_posts(website_id);
```

### Phase 2: Update Application Code
1. Add tenant middleware
2. Update repositories với tenant filtering
3. Update cache keys với website_id prefix
4. Test thoroughly với multiple tenants

### Phase 3: Data Migration
1. Assign existing data to default tenant
2. Migrate tenant-specific data
3. Verify data integrity
4. Enable tenant filtering globally

## Best Practices

### 1. Always Filter by Tenant
```go
// BAD - có thể leak data
posts := db.Find(&posts)

// GOOD - always filter
posts := db.Where("website_id = ?", websiteID).Find(&posts)
```

### 2. Use Composite Keys
```go
// Cache key includes website_id
cacheKey := fmt.Sprintf("website:%d:posts:page:%d", websiteID, page)
```

### 3. Validate Tenant Access
```go
func ValidateTenantAccess(userWebsiteID, resourceWebsiteID uint) error {
    if userWebsiteID != resourceWebsiteID {
        return ErrUnauthorized
    }
    return nil
}
```

### 4. Monitor Cross-tenant Access
- Log mọi attempts cross-tenant access
- Alert on suspicious patterns
- Regular audit của data access

## Monitoring & Alerts

```mermaid
flowchart TD
    subgraph "Monitoring Points"
        A[Cross-tenant Query Attempts]
        B[Missing website_id Filters]
        C[Cache Key Collisions]
        D[Permission Escalations]
    end
    
    subgraph "Alert Actions"
        A --> E[Security Alert]
        B --> F[Dev Alert]
        C --> G[Ops Alert]
        D --> H[Audit Log]
    end
```

## Kết luận

Việc implement data isolation theo website_id là **HIGHLY RECOMMENDED** cho hệ thống multi-tenant vì:

1. **Security**: Tránh hoàn toàn data leakage
2. **Performance**: Query hiệu quả hơn với dataset nhỏ
3. **Scalability**: Dễ dàng scale horizontal
4. **Compliance**: Đáp ứng các yêu cầu về data privacy
5. **Business Flexibility**: Cho phép customization per tenant

Trade-offs chính là complexity trong code, nhưng có thể giải quyết bằng good patterns và tooling.