# Multi-Tenant User Management - <PERSON><PERSON><PERSON>n lý User đa Tenant

## Tổng quan

Hệ thống Multi-Tenant User Management cho phép một user có thể thuộc nhiều tenant kh<PERSON><PERSON> nhau, v<PERSON><PERSON> vai trò (role) khác nhau trên mỗi tenant và website. Tài liệu này mô tả cách thiết kế và implement user management system phức tạp này.

## Mục tiêu

- **Cross-Tenant Access**: User có thể truy cập nhiều tenant
- **Role-based Permissions**: Role khác nhau trên mỗi tenant/website
- **Context Switching**: Chuyển đổi giữa các tenant/website
- **Permission Isolation**: Phân quyền riêng biệt cho từng context
- **Unified Identity**: Single identity across multiple tenants

## Kiến trúc User-Tenant Relationship

### 1. Database Schema Design

```sql
-- Global Users (Cross-tenant identity)
CREATE TABLE blog_global_user (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(320) UNIQUE NOT NULL COMMENT 'Global unique email',
    email_verified_at TIMESTAMP NULL COMMENT 'Email verification timestamp',
    password_hash VARCHAR(255) NOT NULL COMMENT 'Hashed password',
    first_name VARCHAR(50) NOT NULL COMMENT 'User first name',
    last_name VARCHAR(50) NOT NULL COMMENT 'User last name',
    avatar_url VARCHAR(500) NULL COMMENT 'Global avatar URL',
    timezone VARCHAR(50) DEFAULT 'UTC' COMMENT 'User timezone',
    locale VARCHAR(10) DEFAULT 'en' COMMENT 'User locale',
    status ENUM('active', 'inactive', 'suspended', 'banned') DEFAULT 'active' COMMENT 'Global user status',
    last_login_at TIMESTAMP NULL COMMENT 'Last login timestamp',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    UNIQUE KEY uk_blog_global_user_email (email),
    INDEX idx_blog_global_user_status (status),
    INDEX idx_blog_global_user_last_login (last_login_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Global user identities';

-- Tenant User Memberships
CREATE TABLE blog_tenant_user (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    global_user_id INT UNSIGNED NOT NULL COMMENT 'Global user ID',
    tenant_id INT UNSIGNED NOT NULL COMMENT 'Tenant ID',
    local_username VARCHAR(50) NULL COMMENT 'Local username in tenant',
    display_name VARCHAR(100) NULL COMMENT 'Display name in tenant',
    bio TEXT NULL COMMENT 'Bio specific to tenant',
    status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending' COMMENT 'Status in tenant',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Join timestamp',
    invited_by INT UNSIGNED NULL COMMENT 'Who invited this user',
    invitation_token VARCHAR(255) NULL COMMENT 'Invitation token',
    invitation_expires_at TIMESTAMP NULL COMMENT 'Invitation expiration',
    last_activity_at TIMESTAMP NULL COMMENT 'Last activity in tenant',
    preferences JSON NULL COMMENT 'Tenant-specific preferences',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY fk_blog_tenant_user_global (global_user_id) REFERENCES blog_global_user(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_tenant_user_tenant (tenant_id) REFERENCES blog_tenant(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_tenant_user_invited_by (invited_by) REFERENCES blog_global_user(id) ON DELETE SET NULL,
    UNIQUE KEY uk_blog_tenant_user_global_tenant (global_user_id, tenant_id),
    UNIQUE KEY uk_blog_tenant_user_username_tenant (local_username, tenant_id),
    INDEX idx_blog_tenant_user_global_id (global_user_id),
    INDEX idx_blog_tenant_user_tenant_id (tenant_id),
    INDEX idx_blog_tenant_user_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User memberships in tenants';

-- Website User Roles
CREATE TABLE blog_website_user_role (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_user_id INT UNSIGNED NOT NULL COMMENT 'Tenant user membership ID',
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    role_id INT UNSIGNED NOT NULL COMMENT 'Role ID',
    assigned_by INT UNSIGNED NULL COMMENT 'Who assigned this role',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Assignment timestamp',
    expires_at TIMESTAMP NULL COMMENT 'Role expiration',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'Is role active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY fk_blog_website_user_role_tenant_user (tenant_user_id) REFERENCES blog_tenant_user(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_website_user_role_website (website_id) REFERENCES blog_website(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_website_user_role_role (role_id) REFERENCES blog_role(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_website_user_role_assigned_by (assigned_by) REFERENCES blog_global_user(id) ON DELETE SET NULL,
    UNIQUE KEY uk_blog_website_user_role_user_website_role (tenant_user_id, website_id, role_id),
    INDEX idx_blog_website_user_role_tenant_user_id (tenant_user_id),
    INDEX idx_blog_website_user_role_website_id (website_id),
    INDEX idx_blog_website_user_role_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User roles on websites';

-- User Sessions with Context
CREATE TABLE blog_user_session (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    global_user_id INT UNSIGNED NOT NULL COMMENT 'Global user ID',
    current_tenant_id INT UNSIGNED NULL COMMENT 'Currently active tenant',
    current_website_id INT UNSIGNED NULL COMMENT 'Currently active website',
    session_token VARCHAR(255) NOT NULL COMMENT 'Session token',
    refresh_token VARCHAR(255) NOT NULL COMMENT 'Refresh token',
    device_info JSON NULL COMMENT 'Device information',
    ip_address VARCHAR(45) NULL COMMENT 'IP address',
    user_agent TEXT NULL COMMENT 'User agent string',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'Is session active',
    expires_at TIMESTAMP NOT NULL COMMENT 'Session expiration',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY fk_blog_user_session_global_user (global_user_id) REFERENCES blog_global_user(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_user_session_tenant (current_tenant_id) REFERENCES blog_tenant(id) ON DELETE SET NULL,
    FOREIGN KEY fk_blog_user_session_website (current_website_id) REFERENCES blog_website(id) ON DELETE SET NULL,
    UNIQUE KEY uk_blog_user_session_token (session_token),
    UNIQUE KEY uk_blog_user_session_refresh (refresh_token),
    INDEX idx_blog_user_session_global_user_id (global_user_id),
    INDEX idx_blog_user_session_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User sessions with tenant context';
```

### 2. Context-Aware Data Models

```go
package models

import (
    "time"
    "gorm.io/gorm"
)

// Global User Identity
type GlobalUser struct {
    ID              uint32    `gorm:"primarykey" json:"id"`
    Email           string    `gorm:"uniqueIndex;size:320;not null" json:"email"`
    EmailVerifiedAt *time.Time `json:"email_verified_at"`
    PasswordHash    string    `gorm:"size:255;not null" json:"-"`
    FirstName       string    `gorm:"size:50;not null" json:"first_name"`
    LastName        string    `gorm:"size:50;not null" json:"last_name"`
    AvatarURL       string    `gorm:"size:500" json:"avatar_url"`
    Timezone        string    `gorm:"size:50;default:UTC" json:"timezone"`
    Locale          string    `gorm:"size:10;default:en" json:"locale"`
    Status          string    `gorm:"type:enum('active','inactive','suspended','banned');default:active" json:"status"`
    LastLoginAt     *time.Time `json:"last_login_at"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
    DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at"`
    
    // Relationships
    TenantMemberships []TenantUser `gorm:"foreignKey:GlobalUserID" json:"tenant_memberships,omitempty"`
    Sessions          []UserSession `gorm:"foreignKey:GlobalUserID" json:"sessions,omitempty"`
}

// Tenant User Membership
type TenantUser struct {
    ID                  uint32     `gorm:"primarykey" json:"id"`
    GlobalUserID        uint32     `gorm:"not null;index" json:"global_user_id"`
    TenantID            uint32     `gorm:"not null;index" json:"tenant_id"`
    LocalUsername       string     `gorm:"size:50" json:"local_username"`
    DisplayName         string     `gorm:"size:100" json:"display_name"`
    Bio                 string     `gorm:"type:text" json:"bio"`
    Status              string     `gorm:"type:enum('active','inactive','pending','suspended');default:pending" json:"status"`
    JoinedAt            time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"joined_at"`
    InvitedBy           *uint32    `json:"invited_by"`
    InvitationToken     string     `gorm:"size:255" json:"invitation_token"`
    InvitationExpiresAt *time.Time `json:"invitation_expires_at"`
    LastActivityAt      *time.Time `json:"last_activity_at"`
    Preferences         JSON       `gorm:"type:json" json:"preferences"`
    CreatedAt           time.Time  `json:"created_at"`
    UpdatedAt           time.Time  `json:"updated_at"`
    
    // Relationships
    GlobalUser    GlobalUser           `gorm:"foreignKey:GlobalUserID" json:"global_user,omitempty"`
    Tenant        Tenant               `gorm:"foreignKey:TenantID" json:"tenant,omitempty"`
    WebsiteRoles  []WebsiteUserRole    `gorm:"foreignKey:TenantUserID" json:"website_roles,omitempty"`
}

// Website User Role
type WebsiteUserRole struct {
    ID           uint32     `gorm:"primarykey" json:"id"`
    TenantUserID uint32     `gorm:"not null;index" json:"tenant_user_id"`
    WebsiteID    uint32     `gorm:"not null;index" json:"website_id"`
    RoleID       uint32     `gorm:"not null;index" json:"role_id"`
    AssignedBy   *uint32    `json:"assigned_by"`
    AssignedAt   time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"assigned_at"`
    ExpiresAt    *time.Time `json:"expires_at"`
    IsActive     bool       `gorm:"default:true" json:"is_active"`
    CreatedAt    time.Time  `json:"created_at"`
    UpdatedAt    time.Time  `json:"updated_at"`
    
    // Relationships
    TenantUser TenantUser `gorm:"foreignKey:TenantUserID" json:"tenant_user,omitempty"`
    Website    Website    `gorm:"foreignKey:WebsiteID" json:"website,omitempty"`
    Role       Role       `gorm:"foreignKey:RoleID" json:"role,omitempty"`
}

// User Session with Context
type UserSession struct {
    ID               uint32    `gorm:"primarykey" json:"id"`
    GlobalUserID     uint32    `gorm:"not null;index" json:"global_user_id"`
    CurrentTenantID  *uint32   `json:"current_tenant_id"`
    CurrentWebsiteID *uint32   `json:"current_website_id"`
    SessionToken     string    `gorm:"uniqueIndex;size:255;not null" json:"session_token"`
    RefreshToken     string    `gorm:"uniqueIndex;size:255;not null" json:"refresh_token"`
    DeviceInfo       JSON      `gorm:"type:json" json:"device_info"`
    IPAddress        string    `gorm:"size:45" json:"ip_address"`
    UserAgent        string    `gorm:"type:text" json:"user_agent"`
    IsActive         bool      `gorm:"default:true" json:"is_active"`
    ExpiresAt        time.Time `gorm:"not null" json:"expires_at"`
    CreatedAt        time.Time `json:"created_at"`
    UpdatedAt        time.Time `json:"updated_at"`
    
    // Relationships
    GlobalUser    GlobalUser `gorm:"foreignKey:GlobalUserID" json:"global_user,omitempty"`
    CurrentTenant *Tenant    `gorm:"foreignKey:CurrentTenantID" json:"current_tenant,omitempty"`
    CurrentWebsite *Website  `gorm:"foreignKey:CurrentWebsiteID" json:"current_website,omitempty"`
}
```

## User Registration & Invitation Flow

### 1. New User Registration

```mermaid
sequenceDiagram
    participant Client as Client App
    participant Auth as Auth Service
    participant User as User Service
    participant Tenant as Tenant Service
    participant RBAC as RBAC Service
    participant Notification as Notification Service
    participant DB as Database
    
    Client->>Auth: POST /auth/register
    Note over Client,Auth: {email, password, first_name, last_name, tenant_id?, invitation_token?}
    
    Auth->>Auth: Validate input data
    Auth->>DB: Check global email exists
    
    alt Email already exists globally
        Auth->>Client: 409 Email already registered
    end
    
    Auth->>DB: BEGIN TRANSACTION
    
    # Create global user
    Auth->>User: Create global user
    User->>DB: INSERT INTO blog_global_user
    DB->>User: Global user created with ID
    
    # Handle tenant membership
    alt Has invitation token
        Auth->>Tenant: Validate invitation token
        Tenant->>DB: Get invitation details
        DB->>Tenant: Invitation data
        
        Tenant->>User: Create tenant membership
        User->>DB: INSERT INTO blog_tenant_user
        
        Tenant->>RBAC: Assign invited role
        RBAC->>DB: INSERT INTO blog_website_user_role
        
        Tenant->>Notification: Send welcome email
    else Has tenant_id (public signup)
        Auth->>Tenant: Check tenant allows public signup
        
        alt Tenant allows public signup
            User->>DB: INSERT INTO blog_tenant_user (status: pending)
            RBAC->>DB: Assign default role
            Notification->>Notification: Send approval request to admins
        else
            Auth->>Client: 403 Tenant requires invitation
        end
    else No tenant context
        # Global user only, no tenant membership yet
        Auth->>User: Create global user only
    end
    
    Auth->>DB: COMMIT TRANSACTION
    Auth->>Client: 201 User registered successfully
```

### 2. Cross-Tenant Invitation

```mermaid
sequenceDiagram
    participant Admin as Tenant Admin
    participant Auth as Auth Service
    participant User as User Service
    participant Tenant as Tenant Service
    participant Email as Email Service
    participant ExistingUser as Existing User
    
    Admin->>Tenant: POST /tenant/{id}/invite
    Note over Admin,Tenant: {email, role, website_id?, message?}
    
    Tenant->>User: Check if global user exists
    User->>User: Query by email
    
    alt User exists globally
        User->>Tenant: User found, check tenant membership
        Tenant->>Tenant: Check existing membership
        
        alt Already member
            Tenant->>Admin: 409 User already member
        else Not member
            Tenant->>Tenant: Create invitation
            Tenant->>Email: Send invitation email
            Email->>ExistingUser: Invitation email with link
            Tenant->>Admin: 200 Invitation sent
        end
    else User doesn't exist
        Tenant->>Tenant: Create invitation record
        Tenant->>Email: Send signup invitation
        Email->>ExistingUser: Signup invitation email
        Tenant->>Admin: 200 Invitation sent
    end
```

### 3. Accept Invitation Flow

```mermaid
sequenceDiagram
    participant User as User
    participant Auth as Auth Service
    participant Tenant as Tenant Service
    participant RBAC as RBAC Service
    participant DB as Database
    
    User->>Auth: GET /auth/invitation/{token}
    Auth->>Tenant: Validate invitation token
    Tenant->>DB: Get invitation details
    
    alt Invalid or expired token
        Tenant->>User: 404 Invalid invitation
    end
    
    Tenant->>Auth: Valid invitation data
    
    alt User not logged in
        Auth->>User: Redirect to login/register
    else User logged in
        Auth->>Tenant: Accept invitation
        Tenant->>DB: Create tenant membership
        Tenant->>RBAC: Assign role
        RBAC->>DB: Create role assignment
        Tenant->>User: 200 Invitation accepted
    end
```

## Context Switching

### 1. Tenant/Website Context Switching

```go
package services

type ContextSwitchService struct {
    userService   UserService
    tenantService TenantService
    rbacService   RBACService
    cache         cache.Cache
}

// Switch user context to different tenant/website
func (s *ContextSwitchService) SwitchContext(ctx context.Context, userID uint32, tenantID, websiteID uint32) (*UserContext, error) {
    // Validate user has access to tenant
    membership, err := s.userService.GetTenantMembership(ctx, userID, tenantID)
    if err != nil {
        return nil, errors.New("user not member of tenant")
    }
    
    if membership.Status != "active" {
        return nil, errors.New("membership not active")
    }
    
    // Validate website belongs to tenant
    website, err := s.tenantService.GetWebsite(ctx, websiteID)
    if err != nil || website.TenantID != tenantID {
        return nil, errors.New("website not found or not in tenant")
    }
    
    // Get user roles for this context
    roles, err := s.rbacService.GetUserRolesForWebsite(ctx, membership.ID, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Create user context
    userContext := &UserContext{
        GlobalUserID: userID,
        TenantUserID: membership.ID,
        TenantID:     tenantID,
        WebsiteID:    websiteID,
        Roles:        roles,
        Permissions:  s.rbacService.GetPermissionsFromRoles(roles),
        Membership:   membership,
        Website:      website,
    }
    
    // Cache context
    contextKey := fmt.Sprintf("user_context:%d:%d:%d", userID, tenantID, websiteID)
    s.cache.Set(ctx, contextKey, userContext, 30*time.Minute)
    
    return userContext, nil
}

// Get all accessible contexts for user
func (s *ContextSwitchService) GetUserContexts(ctx context.Context, userID uint32) ([]UserContextSummary, error) {
    // Get all tenant memberships
    memberships, err := s.userService.GetTenantMemberships(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    var contexts []UserContextSummary
    
    for _, membership := range memberships {
        if membership.Status != "active" {
            continue
        }
        
        // Get websites in tenant
        websites, err := s.tenantService.GetTenantWebsites(ctx, membership.TenantID)
        if err != nil {
            continue
        }
        
        for _, website := range websites {
            // Check if user has any roles on this website
            roles, err := s.rbacService.GetUserRolesForWebsite(ctx, membership.ID, website.ID)
            if err != nil || len(roles) == 0 {
                continue
            }
            
            contexts = append(contexts, UserContextSummary{
                TenantID:    membership.TenantID,
                TenantName:  membership.Tenant.Name,
                WebsiteID:   website.ID,
                WebsiteName: website.Name,
                WebsiteDomain: website.Domain,
                Roles:       roles,
                LastAccessed: membership.LastActivityAt,
            })
        }
    }
    
    return contexts, nil
}

type UserContext struct {
    GlobalUserID uint32                `json:"global_user_id"`
    TenantUserID uint32                `json:"tenant_user_id"`
    TenantID     uint32                `json:"tenant_id"`
    WebsiteID    uint32                `json:"website_id"`
    Roles        []Role                `json:"roles"`
    Permissions  []string              `json:"permissions"`
    Membership   *TenantUser           `json:"membership"`
    Website      *Website              `json:"website"`
}

type UserContextSummary struct {
    TenantID      uint32     `json:"tenant_id"`
    TenantName    string     `json:"tenant_name"`
    WebsiteID     uint32     `json:"website_id"`
    WebsiteName   string     `json:"website_name"`
    WebsiteDomain string     `json:"website_domain"`
    Roles         []Role     `json:"roles"`
    LastAccessed  *time.Time `json:"last_accessed"`
}
```

### 2. Context-Aware Middleware

```go
package middleware

func TenantContextMiddleware(contextService *ContextSwitchService) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := c.GetUint32("user_id")
        if userID == 0 {
            c.Next()
            return
        }
        
        // Get tenant/website from headers or query params
        tenantID := c.GetUint32("tenant_id")   // From JWT or header
        websiteID := c.GetUint32("website_id") // From domain or header
        
        if tenantID == 0 || websiteID == 0 {
            c.JSON(400, gin.H{"error": "tenant_id and website_id required"})
            c.Abort()
            return
        }
        
        // Check cache first
        contextKey := fmt.Sprintf("user_context:%d:%d:%d", userID, tenantID, websiteID)
        var userContext UserContext
        
        if err := contextService.cache.Get(c.Request.Context(), contextKey, &userContext); err != nil {
            // Cache miss, validate and create context
            ctx, err := contextService.SwitchContext(c.Request.Context(), userID, tenantID, websiteID)
            if err != nil {
                c.JSON(403, gin.H{"error": "access denied to this context"})
                c.Abort()
                return
            }
            userContext = *ctx
        }
        
        // Set context in gin context
        c.Set("user_context", userContext)
        c.Set("tenant_user_id", userContext.TenantUserID)
        c.Set("permissions", userContext.Permissions)
        
        c.Next()
    }
}
```

## Permission System

### 1. Context-Aware Permission Check

```go
package rbac

type PermissionChecker struct {
    rbacService RBACService
    cache       cache.Cache
}

func (p *PermissionChecker) HasPermission(ctx context.Context, userContext UserContext, permission string) bool {
    // Check cached permissions first
    cacheKey := fmt.Sprintf("permissions:%d:%d:%d", userContext.GlobalUserID, userContext.TenantID, userContext.WebsiteID)
    
    var permissions []string
    if err := p.cache.Get(ctx, cacheKey, &permissions); err != nil {
        // Cache miss, get fresh permissions
        permissions = p.rbacService.GetUserPermissions(ctx, userContext.TenantUserID, userContext.WebsiteID)
        p.cache.Set(ctx, cacheKey, permissions, 15*time.Minute)
    }
    
    return contains(permissions, permission)
}

func (p *PermissionChecker) HasRole(ctx context.Context, userContext UserContext, roleSlug string) bool {
    for _, role := range userContext.Roles {
        if role.Slug == roleSlug {
            return true
        }
    }
    return false
}

func (p *PermissionChecker) CanAccessResource(ctx context.Context, userContext UserContext, resourceType string, resourceID uint32) bool {
    // Check if user has general permission for resource type
    if !p.HasPermission(ctx, userContext, fmt.Sprintf("%s.read", resourceType)) {
        return false
    }
    
    // Check resource-specific permissions
    switch resourceType {
    case "post":
        return p.canAccessPost(ctx, userContext, resourceID)
    case "media":
        return p.canAccessMedia(ctx, userContext, resourceID)
    default:
        return false
    }
}

func (p *PermissionChecker) canAccessPost(ctx context.Context, userContext UserContext, postID uint32) bool {
    // Get post details
    post, err := p.rbacService.GetPost(ctx, postID)
    if err != nil {
        return false
    }
    
    // Check if post belongs to current website
    if post.WebsiteID != userContext.WebsiteID {
        return false
    }
    
    // Check if user is author
    if post.AuthorID == userContext.TenantUserID {
        return true
    }
    
    // Check if user has editor permissions
    return p.HasPermission(ctx, userContext, "post.edit_all")
}
```

### 2. Permission Middleware

```go
func RequirePermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userContext, exists := c.Get("user_context")
        if !exists {
            c.JSON(401, gin.H{"error": "authentication required"})
            c.Abort()
            return
        }
        
        ctx := userContext.(UserContext)
        permissionChecker := c.MustGet("permission_checker").(*PermissionChecker)
        
        if !permissionChecker.HasPermission(c.Request.Context(), ctx, permission) {
            c.JSON(403, gin.H{"error": "insufficient permissions"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

func RequireRole(roleSlug string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userContext, exists := c.Get("user_context")
        if !exists {
            c.JSON(401, gin.H{"error": "authentication required"})
            c.Abort()
            return
        }
        
        ctx := userContext.(UserContext)
        permissionChecker := c.MustGet("permission_checker").(*PermissionChecker)
        
        if !permissionChecker.HasRole(c.Request.Context(), ctx, roleSlug) {
            c.JSON(403, gin.H{"error": "insufficient role"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

## API Endpoints

### 1. User Context Management

```go
// Get user's accessible contexts
// GET /api/v1/user/contexts
func (h *UserHandler) GetUserContexts(c *gin.Context) {
    userID := c.GetUint32("user_id")
    
    contexts, err := h.contextService.GetUserContexts(c.Request.Context(), userID)
    if err != nil {
        c.JSON(500, gin.H{"error": "failed to get contexts"})
        return
    }
    
    c.JSON(200, gin.H{
        "contexts": contexts,
    })
}

// Switch user context
// POST /api/v1/user/context/switch
func (h *UserHandler) SwitchContext(c *gin.Context) {
    var req struct {
        TenantID  uint32 `json:"tenant_id" binding:"required"`
        WebsiteID uint32 `json:"website_id" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    userID := c.GetUint32("user_id")
    
    userContext, err := h.contextService.SwitchContext(c.Request.Context(), userID, req.TenantID, req.WebsiteID)
    if err != nil {
        c.JSON(403, gin.H{"error": err.Error()})
        return
    }
    
    // Update session with new context
    sessionToken := c.GetString("session_token")
    h.authService.UpdateSessionContext(c.Request.Context(), sessionToken, req.TenantID, req.WebsiteID)
    
    c.JSON(200, gin.H{
        "context": userContext,
        "message": "context switched successfully",
    })
}

// Get current user context
// GET /api/v1/user/context/current
func (h *UserHandler) GetCurrentContext(c *gin.Context) {
    userContext, exists := c.Get("user_context")
    if !exists {
        c.JSON(404, gin.H{"error": "no active context"})
        return
    }
    
    c.JSON(200, gin.H{
        "context": userContext,
    })
}
```

### 2. Tenant Invitation Management

```go
// Invite user to tenant
// POST /api/v1/tenant/{tenant_id}/invite
func (h *TenantHandler) InviteUser(c *gin.Context) {
    tenantID := c.GetUint32("tenant_id")
    
    var req struct {
        Email     string   `json:"email" binding:"required,email"`
        RoleID    uint32   `json:"role_id" binding:"required"`
        WebsiteID *uint32  `json:"website_id"`
        Message   string   `json:"message"`
        ExpiresIn int      `json:"expires_in"` // hours
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    invitation, err := h.tenantService.InviteUser(c.Request.Context(), InviteUserRequest{
        TenantID:  tenantID,
        Email:     req.Email,
        RoleID:    req.RoleID,
        WebsiteID: req.WebsiteID,
        Message:   req.Message,
        ExpiresIn: time.Duration(req.ExpiresIn) * time.Hour,
        InvitedBy: c.GetUint32("user_id"),
    })
    
    if err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(201, gin.H{
        "invitation": invitation,
        "message": "invitation sent successfully",
    })
}

// Accept invitation
// POST /api/v1/invitation/{token}/accept
func (h *TenantHandler) AcceptInvitation(c *gin.Context) {
    token := c.Param("token")
    userID := c.GetUint32("user_id")
    
    membership, err := h.tenantService.AcceptInvitation(c.Request.Context(), token, userID)
    if err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, gin.H{
        "membership": membership,
        "message": "invitation accepted successfully",
    })
}

// List tenant members
// GET /api/v1/tenant/{tenant_id}/members
func (h *TenantHandler) GetTenantMembers(c *gin.Context) {
    tenantID := c.GetUint32("tenant_id")
    
    members, err := h.tenantService.GetTenantMembers(c.Request.Context(), tenantID)
    if err != nil {
        c.JSON(500, gin.H{"error": "failed to get members"})
        return
    }
    
    c.JSON(200, gin.H{
        "members": members,
    })
}
```

## Best Practices

### 1. Security Considerations
- **Context Validation**: Always validate user access to tenant/website context
- **Permission Caching**: Cache permissions with short TTL to balance performance and security
- **Session Context**: Store current context in session for seamless experience
- **Audit Logging**: Log all context switches and permission checks

### 2. Performance Optimization
- **Context Caching**: Cache user contexts to avoid repeated database queries
- **Permission Batching**: Batch permission checks where possible
- **Lazy Loading**: Load role details only when needed
- **Index Optimization**: Proper database indexes for multi-tenant queries

### 3. User Experience
- **Context Picker**: Provide clear UI for context switching
- **Context Persistence**: Remember last used context per user
- **Role Visibility**: Show user's role on current context
- **Permission Feedback**: Clear error messages for permission denials

### 4. Development Guidelines
- **Context Injection**: Always inject user context in request handlers
- **Permission Decorators**: Use middleware for permission checks
- **Resource Scoping**: Scope all queries to current tenant/website context
- **Migration Strategy**: Plan for migrating existing single-tenant users