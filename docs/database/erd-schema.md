# Entity Relationship Diagram (ERD) - Database Schema by <PERSON><PERSON><PERSON>

## Tổng quan

Tài liệu nà<PERSON> mô tả complete Entity Relationship Diagram cho Blog API v3 database schema, được tổ chức theo modules với prefix groups để dễ quản lý và hiểu.

## Database Overview

### Database Statistics
- **Total Tables**: 45+ tables
- **Engine**: InnoDB
- **Charset**: utf8mb4_unicode_ci
- **Primary Keys**: INT UNSIGNED AUTO_INCREMENT
- **Foreign Keys**: Cascading deletes where appropriate
- **Indexes**: Optimized for multi-tenant queries

## Module Organization

Database được tổ chức theo các module groups với module-based prefix naming convention:

### Core System Modules
- **`tenant_organizations`** - Tenant management
- **`website_sites`** - Website management
- **`user_accounts`** - User management
- **`auth_sessions`** - Authentication system
- **`rbac_roles`** - RBAC system

### Content Modules
- **`blog_posts`** - Blog posts and content
- **`blog_categories`** - Content categorization
- **`blog_tags`** - Content tagging
- **`blog_comments`** - Comments system
- **`media_files`** - Media management

### Communication Modules
- **`notification_messages`** - Notification system
- **`email_templates`** - Email system
- **`socket_connections`** - Real-time communication

### Feature Modules
- **`payment_transactions`** - Payment processing
- **`seo_meta_tags`** - SEO optimization
- **`api_keys`** - API management
- **`onboarding_journeys`** - User onboarding

## 1. Core System - Tenant & User Management

### Core Tenant & User Management

```mermaid
erDiagram
    %% Core tenant and user management
    tenant_organizations {
        INT_UNSIGNED id PK
        VARCHAR_100 name
        VARCHAR_100 slug UK
        VARCHAR_320 email UK
        VARCHAR_20 phone
        VARCHAR_500 logo_url
        ENUM status "active,inactive,suspended"
        ENUM plan_type "free,basic,pro,enterprise"
        JSON settings
        TIMESTAMP created_at
        TIMESTAMP updated_at
        TIMESTAMP deleted_at
    }

    blog_website {
        INT_UNSIGNED id PK
        INT_UNSIGNED tenant_id FK
        VARCHAR_100 name
        VARCHAR_100 slug
        VARCHAR_255 domain UK
        JSON alternate_domains
        VARCHAR_255 title
        TEXT description
        VARCHAR_500 logo_url
        VARCHAR_500 favicon_url
        JSON theme_config
        JSON seo_config
        ENUM status "active,inactive,maintenance"
        TINYINT is_default
        TIMESTAMP created_at
        TIMESTAMP updated_at
        TIMESTAMP deleted_at
    }

    blog_user {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_320 email
        TIMESTAMP email_verified_at
        VARCHAR_255 password_hash
        VARCHAR_50 first_name
        VARCHAR_50 last_name
        VARCHAR_50 username
        VARCHAR_500 avatar_url
        TEXT bio
        VARCHAR_20 phone
        DATE birth_date
        ENUM gender "male,female,other"
        VARCHAR_50 timezone
        VARCHAR_10 locale
        ENUM status "active,inactive,pending,suspended,banned"
        TINYINT is_verified
        TINYINT is_admin
        TIMESTAMP last_login_at
        VARCHAR_45 last_login_ip
        INT_UNSIGNED failed_login_attempts
        TIMESTAMP locked_until
        TIMESTAMP password_changed_at
        JSON preferences
        JSON metadata
        TIMESTAMP created_at
        TIMESTAMP updated_at
        TIMESTAMP deleted_at
    }

    blog_user_session {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        VARCHAR_255 session_token UK
        VARCHAR_255 refresh_token UK
        JSON device_info
        VARCHAR_45 ip_address
        TEXT user_agent
        TINYINT is_active
        TIMESTAMP expires_at
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    %% Relationships
    blog_tenant ||--o{ blog_website : "has"
    blog_website ||--o{ blog_user : "has_users"
    blog_user ||--o{ blog_user_session : "has_sessions"
```

## 2. RBAC System - Role-Based Access Control

```mermaid
erDiagram
    %% RBAC System
    blog_role {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_100 name
        VARCHAR_100 slug
        TEXT description
        TINYINT is_system
        TINYINT is_default
        TINYINT level
        JSON permissions
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_permission {
        INT_UNSIGNED id PK
        VARCHAR_100 name
        VARCHAR_100 slug UK
        TEXT description
        VARCHAR_50 category
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_user_role {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        INT_UNSIGNED role_id FK
        INT_UNSIGNED assigned_by FK
        TIMESTAMP assigned_at
        TIMESTAMP expires_at
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_role_permission {
        INT_UNSIGNED id PK
        INT_UNSIGNED role_id FK
        INT_UNSIGNED permission_id FK
        TIMESTAMP created_at
    }

    %% Relationships
    blog_website ||--o{ blog_role : "has_roles"
    blog_role ||--o{ blog_user_role : "assigned_to"
    blog_user ||--o{ blog_user_role : "has_roles"
    blog_role ||--o{ blog_role_permission : "has_permissions"
    blog_permission ||--o{ blog_role_permission : "granted_to"
```

## 3. Blog Content System

### Posts, Categories & Tags

```mermaid
erDiagram
    %% Blog Content System
    blog_category {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_100 name
        VARCHAR_100 slug
        TEXT description
        INT_UNSIGNED parent_id FK
        INT_UNSIGNED lft "Left boundary"
        INT_UNSIGNED rgt "Right boundary"
        TINYINT level
        SMALLINT sort_order
        VARCHAR_500 image_url
        VARCHAR_255 meta_title
        VARCHAR_500 meta_description
        TINYINT is_active
        INT_UNSIGNED post_count
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_tag {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_100 name
        VARCHAR_100 slug
        TEXT description
        VARCHAR_7 color "Hex color"
        INT_UNSIGNED post_count
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_post {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_255 title
        VARCHAR_255 slug
        TEXT excerpt
        LONGTEXT content
        ENUM content_type "html,markdown,json"
        INT_UNSIGNED author_id FK
        INT_UNSIGNED category_id FK
        VARCHAR_500 featured_image_url
        ENUM status "draft,review,published,archived"
        ENUM visibility "public,private,protected,password"
        VARCHAR_255 password
        TINYINT is_featured
        TINYINT is_sticky
        TINYINT allow_comments
        INT_UNSIGNED view_count
        INT_UNSIGNED like_count
        INT_UNSIGNED comment_count
        INT_UNSIGNED share_count
        INT_UNSIGNED reading_time "minutes"
        TIMESTAMP published_at
        TIMESTAMP scheduled_at
        VARCHAR_255 meta_title
        VARCHAR_500 meta_description
        VARCHAR_500 meta_keywords
        TINYINT seo_score "0-100"
        TIMESTAMP created_at
        TIMESTAMP updated_at
        TIMESTAMP deleted_at
    }

    blog_post_tag {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED post_id FK
        INT_UNSIGNED tag_id FK
        TIMESTAMP created_at
    }

    blog_author {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        VARCHAR_100 display_name
        VARCHAR_100 slug
        TEXT bio
        VARCHAR_500 avatar_url
        VARCHAR_500 website_url
        JSON social_links
        TINYINT is_active
        INT_UNSIGNED post_count
        INT_UNSIGNED follower_count
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    %% Relationships
    blog_website ||--o{ blog_category : "has_categories"
    blog_website ||--o{ blog_tag : "has_tags"
    blog_website ||--o{ blog_post : "has_posts"
    blog_website ||--o{ blog_author : "has_authors"
    blog_category ||--o{ blog_category : "parent_child"
    blog_category ||--o{ blog_post : "categorizes"
    blog_tag ||--o{ blog_post_tag : "tags"
    blog_post ||--o{ blog_post_tag : "tagged_with"
    blog_author ||--o{ blog_post : "writes"
    blog_user ||--o{ blog_author : "is_author"
```

## Core Entity Definitions

### 1. Tenant & User Management

#### Global User Entity
```sql
global_user {
    INT UNSIGNED id PK
    VARCHAR(320) email UK "Global unique email"
    TIMESTAMP email_verified_at
    VARCHAR(255) password_hash
    VARCHAR(50) first_name
    VARCHAR(50) last_name
    VARCHAR(500) avatar_url
    VARCHAR(50) timezone
    VARCHAR(10) locale
    ENUM status "active,inactive,suspended,banned"
    TIMESTAMP last_login_at
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

#### Tenant Entity
```sql
tenant {
    INT UNSIGNED id PK
    VARCHAR(100) name
    VARCHAR(100) slug UK
    VARCHAR(320) email UK
    VARCHAR(20) phone
    VARCHAR(500) logo_url
    ENUM status "active,inactive,suspended"
    ENUM plan_type "free,basic,pro,enterprise"
    JSON settings
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

#### Website Entity
```sql
website {
    INT UNSIGNED id PK
    INT UNSIGNED tenant_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    VARCHAR(255) domain UK
    JSON alternate_domains
    VARCHAR(255) title
    TEXT description
    VARCHAR(500) logo_url
    VARCHAR(500) favicon_url
    JSON theme_config
    JSON seo_config
    ENUM status "active,inactive,maintenance"
    TINYINT is_default
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

#### Tenant User Membership
```sql
tenant_user {
    INT UNSIGNED id PK
    INT UNSIGNED global_user_id FK
    INT UNSIGNED tenant_id FK
    VARCHAR(50) local_username
    VARCHAR(100) display_name
    TEXT bio
    ENUM status "active,inactive,pending,suspended"
    TIMESTAMP joined_at
    INT UNSIGNED invited_by FK
    VARCHAR(255) invitation_token
    TIMESTAMP invitation_expires_at
    TIMESTAMP last_activity_at
    JSON preferences
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 2. RBAC System

#### Role Entity
```sql
role {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    TEXT description
    TINYINT is_system
    TINYINT is_default
    TINYINT level
    JSON permissions
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Permission Entity
```sql
permission {
    INT UNSIGNED id PK
    VARCHAR(100) name
    VARCHAR(100) slug UK
    TEXT description
    VARCHAR(50) category
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Website User Role
```sql
website_user_role {
    INT UNSIGNED id PK
    INT UNSIGNED tenant_user_id FK
    INT UNSIGNED website_id FK
    INT UNSIGNED role_id FK
    INT UNSIGNED assigned_by FK
    TIMESTAMP assigned_at
    TIMESTAMP expires_at
    TINYINT is_active
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 3. Blog Content System

#### Category Entity (Nested Set Model)
```sql
category {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    TEXT description
    INT UNSIGNED parent_id FK
    INT UNSIGNED lft "Left boundary"
    INT UNSIGNED rgt "Right boundary"
    TINYINT level
    SMALLINT sort_order
    VARCHAR(500) image_url
    VARCHAR(255) meta_title
    VARCHAR(500) meta_description
    TINYINT is_active
    INT UNSIGNED post_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Tag Entity
```sql
tag {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    TEXT description
    VARCHAR(7) color "Hex color"
    INT UNSIGNED post_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Post Entity
```sql
post {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(255) title
    VARCHAR(255) slug
    TEXT excerpt
    LONGTEXT content
    ENUM content_type "html,markdown,json"
    INT UNSIGNED author_id FK
    INT UNSIGNED category_id FK
    VARCHAR(500) featured_image_url
    ENUM status "draft,review,published,archived"
    ENUM visibility "public,private,protected,password"
    VARCHAR(255) password
    TINYINT is_featured
    TINYINT is_sticky
    TINYINT allow_comments
    INT UNSIGNED view_count
    INT UNSIGNED like_count
    INT UNSIGNED comment_count
    INT UNSIGNED share_count
    INT UNSIGNED reading_time "minutes"
    TIMESTAMP published_at
    TIMESTAMP scheduled_at
    VARCHAR(255) meta_title
    VARCHAR(500) meta_description
    VARCHAR(500) meta_keywords
    TINYINT seo_score "0-100"
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

#### Author Entity
```sql
author {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED user_id FK
    VARCHAR(100) display_name
    VARCHAR(100) slug
    TEXT bio
    VARCHAR(500) avatar_url
    VARCHAR(500) website_url
    JSON social_links
    TINYINT is_active
    INT UNSIGNED post_count
    INT UNSIGNED follower_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

## 4. Media Management System

```mermaid
erDiagram
    %% Media Management
    blog_media {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        VARCHAR_255 filename
        VARCHAR_255 stored_filename
        VARCHAR_255 title
        VARCHAR_255 alt_text
        TEXT description
        VARCHAR_100 mime_type
        INT_UNSIGNED file_size "bytes"
        INT_UNSIGNED width "pixels"
        INT_UNSIGNED height "pixels"
        INT_UNSIGNED duration "seconds"
        VARCHAR_20 storage_driver "local,s3,minio"
        VARCHAR_500 storage_path
        VARCHAR_500 public_url
        VARCHAR_500 cdn_url
        JSON variants "thumbnails,sizes"
        JSON metadata
        VARCHAR_500 folder_path
        TINYINT is_optimized
        INT_UNSIGNED download_count
        TIMESTAMP created_at
        TIMESTAMP updated_at
        TIMESTAMP deleted_at
    }

    blog_media_folder {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_255 name
        VARCHAR_500 path
        INT_UNSIGNED parent_id FK
        TEXT description
        TINYINT is_public
        INT_UNSIGNED file_count
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    %% Relationships
    blog_website ||--o{ blog_media : "stores"
    blog_user ||--o{ blog_media : "uploads"
    blog_website ||--o{ blog_media_folder : "has_folders"
    blog_media_folder ||--o{ blog_media_folder : "parent_child"
    blog_media_folder ||--o{ blog_media : "contains"
```

## 5. Notification System

```mermaid
erDiagram
    %% Notification System
    blog_notification_template {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_100 name
        VARCHAR_100 slug
        VARCHAR_255 subject
        LONGTEXT body_html
        LONGTEXT body_text
        JSON variables
        TINYINT is_active
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_notification {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        INT_UNSIGNED template_id FK
        VARCHAR_50 type
        VARCHAR_255 title
        TEXT message
        JSON data
        JSON channels "email,sms,push,slack"
        ENUM status "pending,sent,failed,delivered,read"
        ENUM priority "low,normal,high,urgent"
        TIMESTAMP scheduled_at
        TIMESTAMP sent_at
        TIMESTAMP read_at
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_notification_log {
        INT_UNSIGNED id PK
        INT_UNSIGNED notification_id FK
        VARCHAR_50 channel
        ENUM status "pending,sent,failed,delivered"
        TEXT error_message
        JSON response_data
        TIMESTAMP sent_at
        TIMESTAMP created_at
    }

    %% Relationships
    blog_website ||--o{ blog_notification_template : "has_templates"
    blog_website ||--o{ blog_notification : "sends"
    blog_user ||--o{ blog_notification : "receives"
    blog_notification_template ||--o{ blog_notification : "uses_template"
    blog_notification ||--o{ blog_notification_log : "has_logs"
```

### 4. Media Management

#### Media Entity
```sql
media {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED user_id FK
    VARCHAR(255) filename
    VARCHAR(255) stored_filename
    VARCHAR(255) title
    VARCHAR(255) alt_text
    TEXT description
    VARCHAR(100) mime_type
    INT UNSIGNED file_size "bytes"
    INT UNSIGNED width "pixels"
    INT UNSIGNED height "pixels"
    INT UNSIGNED duration "seconds"
    VARCHAR(20) storage_driver "local,s3,minio"
    VARCHAR(500) storage_path
    VARCHAR(500) public_url
    VARCHAR(500) cdn_url
    JSON variants "thumbnails,sizes"
    JSON metadata
    VARCHAR(500) folder_path
    TINYINT is_optimized
    INT UNSIGNED download_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

### 5. Notification System

#### Notification Template Entity
```sql
notification_template {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    VARCHAR(255) subject
    LONGTEXT body_html
    LONGTEXT body_text
    JSON variables
    TINYINT is_active
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Notification Entity
```sql
notification {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED user_id FK
    VARCHAR(50) type
    VARCHAR(255) title
    TEXT message
    JSON data
    JSON channels "email,sms,push,slack"
    ENUM status "pending,sent,failed,delivered,read"
    ENUM priority "low,normal,high,urgent"
    TIMESTAMP scheduled_at
    TIMESTAMP sent_at
    TIMESTAMP read_at
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

## 6. Payment System

```mermaid
erDiagram
    %% Payment System
    blog_subscription_plan {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_100 name
        VARCHAR_100 slug
        TEXT description
        DECIMAL_10_2 price
        VARCHAR_3 currency
        ENUM billing_period "monthly,yearly,lifetime"
        JSON features
        INT max_users
        INT max_storage "bytes"
        INT max_bandwidth "bytes"
        TINYINT is_active
        TINYINT is_featured
        INT sort_order
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_customer {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        VARCHAR_100 customer_id "External customer ID"
        VARCHAR_255 billing_email
        JSON billing_address
        JSON payment_methods
        ENUM status "active,inactive,suspended"
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_subscription {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED customer_id FK
        INT_UNSIGNED plan_id FK
        VARCHAR_100 subscription_id "External subscription ID"
        ENUM status "active,cancelled,expired,past_due"
        TIMESTAMP current_period_start
        TIMESTAMP current_period_end
        TIMESTAMP trial_start
        TIMESTAMP trial_end
        TIMESTAMP cancelled_at
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_payment {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED customer_id FK
        INT_UNSIGNED subscription_id FK
        VARCHAR_100 payment_id "External payment ID"
        DECIMAL_10_2 amount
        VARCHAR_3 currency
        ENUM status "pending,processing,completed,failed,cancelled,refunded"
        ENUM payment_method "card,paypal,bank_transfer,crypto"
        VARCHAR_100 provider "stripe,paypal,square"
        TEXT description
        JSON provider_data
        JSON metadata
        TIMESTAMP processed_at
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_invoice {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED customer_id FK
        INT_UNSIGNED payment_id FK
        VARCHAR_100 invoice_number
        DECIMAL_10_2 subtotal
        DECIMAL_10_2 tax_amount
        DECIMAL_10_2 total_amount
        VARCHAR_3 currency
        ENUM status "draft,sent,paid,overdue,cancelled"
        TIMESTAMP due_date
        TIMESTAMP paid_at
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    %% Relationships
    blog_website ||--o{ blog_subscription_plan : "offers_plans"
    blog_website ||--o{ blog_customer : "has_customers"
    blog_user ||--o{ blog_customer : "is_customer"
    blog_customer ||--o{ blog_subscription : "has_subscriptions"
    blog_subscription_plan ||--o{ blog_subscription : "subscribed_to"
    blog_customer ||--o{ blog_payment : "makes_payments"
    blog_subscription ||--o{ blog_payment : "payment_for"
    blog_payment ||--o{ blog_invoice : "generates_invoice"
```

## 7. SEO Management System

```mermaid
erDiagram
    %% SEO System
    blog_seo_meta {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_50 entity_type "post,page,category"
        INT_UNSIGNED entity_id
        VARCHAR_255 title
        VARCHAR_500 description
        VARCHAR_500 keywords
        VARCHAR_255 canonical_url
        JSON og_tags "Open Graph"
        JSON twitter_tags
        JSON structured_data
        VARCHAR_255 robots
        TINYINT seo_score "0-100"
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_url_redirect {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_500 from_url
        VARCHAR_500 to_url
        ENUM redirect_type "301,302,307,308"
        TINYINT is_active
        INT hit_count
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_sitemap {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_255 name
        VARCHAR_500 url
        ENUM type "main,posts,pages,categories,tags"
        JSON urls
        TIMESTAMP last_generated
        TINYINT is_active
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    %% Relationships
    blog_website ||--o{ blog_seo_meta : "has_seo"
    blog_website ||--o{ blog_url_redirect : "has_redirects"
    blog_website ||--o{ blog_sitemap : "has_sitemaps"
```

### 6. Payment System

#### Subscription Plan Entity
```sql
subscription_plan {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    TEXT description
    DECIMAL(10,2) price
    VARCHAR(3) currency
    ENUM billing_period "monthly,yearly,lifetime"
    JSON features
    INT max_users
    INT max_storage "bytes"
    INT max_bandwidth "bytes"
    TINYINT is_active
    TINYINT is_featured
    INT sort_order
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Payment Entity
```sql
payment {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED customer_id FK
    VARCHAR(100) payment_id "External payment ID"
    DECIMAL(10,2) amount
    VARCHAR(3) currency
    ENUM status "pending,processing,completed,failed,cancelled,refunded"
    ENUM payment_method "card,paypal,bank_transfer,crypto"
    VARCHAR(100) provider "stripe,paypal,square"
    TEXT description
    JSON provider_data
    JSON metadata
    TIMESTAMP processed_at
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 7. SEO Management

#### SEO Meta Entity
```sql
seo_meta {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(50) entity_type "post,page,category"
    INT UNSIGNED entity_id
    VARCHAR(255) title
    VARCHAR(500) description
    VARCHAR(500) keywords
    VARCHAR(255) canonical_url
    JSON og_tags "Open Graph"
    JSON twitter_tags
    JSON structured_data
    VARCHAR(255) robots
    TINYINT seo_score "0-100"
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### URL Redirect Entity
```sql
url_redirect {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(500) from_url
    VARCHAR(500) to_url
    ENUM redirect_type "301,302,307,308"
    TINYINT is_active
    INT hit_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

## 8. Real-time Communication System

```mermaid
erDiagram
    %% Socket/Real-time System
    blog_socket_connection {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        VARCHAR_255 connection_id UK
        VARCHAR_255 socket_id
        VARCHAR_45 ip_address
        TEXT user_agent
        JSON metadata
        TINYINT is_active
        TIMESTAMP connected_at
        TIMESTAMP last_ping_at
        TIMESTAMP disconnected_at
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_socket_room {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_100 name
        VARCHAR_100 slug
        TEXT description
        ENUM type "public,private,broadcast"
        INT max_connections
        INT current_connections
        JSON settings
        TINYINT is_active
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_socket_room_member {
        INT_UNSIGNED id PK
        INT_UNSIGNED room_id FK
        INT_UNSIGNED connection_id FK
        ENUM role "member,moderator,admin"
        TIMESTAMP joined_at
        TIMESTAMP left_at
        TINYINT is_active
        TIMESTAMP created_at
    }

    blog_socket_message {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED room_id FK
        INT_UNSIGNED connection_id FK
        VARCHAR_50 type "text,image,file,system"
        TEXT content
        JSON data
        TINYINT is_broadcast
        TIMESTAMP created_at
    }

    %% Relationships
    blog_website ||--o{ blog_socket_connection : "has_connections"
    blog_user ||--o{ blog_socket_connection : "connects"
    blog_website ||--o{ blog_socket_room : "has_rooms"
    blog_socket_room ||--o{ blog_socket_room_member : "has_members"
    blog_socket_connection ||--o{ blog_socket_room_member : "joins_rooms"
    blog_socket_room ||--o{ blog_socket_message : "contains_messages"
    blog_socket_connection ||--o{ blog_socket_message : "sends_messages"
```

## 9. Analytics & Activity Tracking

```mermaid
erDiagram
    %% Analytics System
    blog_analytics_event {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        VARCHAR_50 event_type "page_view,click,download,search"
        VARCHAR_255 event_name
        VARCHAR_500 page_url
        VARCHAR_500 referrer_url
        VARCHAR_45 ip_address
        TEXT user_agent
        JSON event_data
        JSON session_data
        TIMESTAMP created_at
    }

    blog_activity_log {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        VARCHAR_50 action "create,update,delete,login,logout"
        VARCHAR_100 entity_type "post,user,category"
        INT_UNSIGNED entity_id
        JSON old_values
        JSON new_values
        VARCHAR_45 ip_address
        TEXT user_agent
        TIMESTAMP created_at
    }

    blog_page_view {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED user_id FK
        VARCHAR_500 url
        VARCHAR_255 title
        VARCHAR_500 referrer
        VARCHAR_45 ip_address
        TEXT user_agent
        INT duration_seconds
        TIMESTAMP viewed_at
        TIMESTAMP created_at
    }

    %% Relationships
    blog_website ||--o{ blog_analytics_event : "tracks_events"
    blog_user ||--o{ blog_analytics_event : "generates_events"
    blog_website ||--o{ blog_activity_log : "logs_activities"
    blog_user ||--o{ blog_activity_log : "performs_activities"
    blog_website ||--o{ blog_page_view : "tracks_views"
    blog_user ||--o{ blog_page_view : "views_pages"
```

### 8. Real-time Communication

#### Socket Connection Entity
```sql
socket_connection {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED user_id FK
    VARCHAR(255) connection_id
    VARCHAR(255) socket_id
    JSON metadata
    TINYINT is_active
    TIMESTAMP connected_at
    TIMESTAMP last_ping_at
    TIMESTAMP disconnected_at
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

## 10. Comments & Engagement System

```mermaid
erDiagram
    %% Comments System
    blog_comment {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        INT_UNSIGNED post_id FK
        INT_UNSIGNED user_id FK
        INT_UNSIGNED parent_id FK
        TEXT content
        ENUM status "pending,approved,rejected,spam"
        VARCHAR_45 ip_address
        TEXT user_agent
        INT like_count
        INT dislike_count
        TINYINT is_pinned
        TIMESTAMP created_at
        TIMESTAMP updated_at
        TIMESTAMP deleted_at
    }

    blog_comment_like {
        INT_UNSIGNED id PK
        INT_UNSIGNED comment_id FK
        INT_UNSIGNED user_id FK
        ENUM type "like,dislike"
        TIMESTAMP created_at
    }

    %% Relationships
    blog_website ||--o{ blog_comment : "has_comments"
    blog_post ||--o{ blog_comment : "receives_comments"
    blog_user ||--o{ blog_comment : "writes_comments"
    blog_comment ||--o{ blog_comment : "parent_child"
    blog_comment ||--o{ blog_comment_like : "receives_likes"
    blog_user ||--o{ blog_comment_like : "likes_comments"
```

## 11. System Configuration & Settings

```mermaid
erDiagram
    %% System Tables
    blog_setting {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_100 key UK
        TEXT value
        VARCHAR_50 type "string,number,boolean,json"
        TEXT description
        TINYINT is_public
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_plugin_config {
        INT_UNSIGNED id PK
        INT_UNSIGNED website_id FK
        VARCHAR_100 plugin_name
        VARCHAR_100 config_key
        TEXT config_value
        TINYINT is_active
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    blog_migration {
        INT_UNSIGNED id PK
        VARCHAR_255 migration
        INT batch
        TIMESTAMP created_at
    }

    blog_seeder_log {
        INT_UNSIGNED id PK
        VARCHAR_255 seeder_name
        VARCHAR_50 entity_type "tenant,website,user"
        INT_UNSIGNED entity_id
        JSON seeded_data
        TIMESTAMP created_at
    }

    %% Relationships
    blog_website ||--o{ blog_setting : "has_settings"
    blog_website ||--o{ blog_plugin_config : "configures_plugins"
```

## Database Relationships Summary

### Module-Based Relationships

#### Core System Relationships
1. **blog_tenant → blog_website** (1:N)
   - One tenant can have multiple websites
   - Website belongs to exactly one tenant

2. **blog_website → blog_user** (1:N)
   - Users are scoped to specific websites
   - Multi-tenant isolation through website_id

3. **blog_user → blog_user_session** (1:N)
   - Users can have multiple active sessions
   - Session management per website context

#### RBAC Relationships
4. **blog_website → blog_role** (1:N)
   - Roles are website-specific
   - Each website defines its own role hierarchy

5. **blog_user → blog_user_role** (N:M)
   - Users can have multiple roles per website
   - Role assignments are website-scoped

#### Content Relationships
6. **blog_website → blog_post** (1:N)
   - Posts belong to specific website
   - Content isolation per website

7. **blog_post → blog_tag** (N:M via blog_post_tag)
   - Posts can have multiple tags
   - Tags can be used by multiple posts

8. **blog_category → blog_post** (1:N with hierarchy)
   - Categories use nested set model
   - Posts belong to one primary category

#### Feature Module Relationships
9. **blog_website → blog_media** (1:N)
   - Media files are website-scoped
   - Organized in folder hierarchy

10. **blog_website → blog_notification** (1:N)
    - Notifications are website-specific
    - Template-based notification system

11. **blog_website → blog_payment** (1:N)
    - Payment processing per website
    - Subscription management per tenant

### Key Constraints

1. **Tenant Isolation**
   - All content tables include website_id
   - Foreign keys ensure data isolation

2. **User Management**
   - Global users with tenant-specific memberships
   - Role assignments are website-scoped

3. **Content Hierarchy**
   - Nested categories with lft/rgt boundaries
   - Post status workflow enforcement

4. **Referential Integrity**
   - Cascade deletes for dependent data
   - Restrict deletes for critical references

## Index Strategy by Module

### Core System Indexes
```sql
-- blog_tenant
UNIQUE KEY uk_blog_tenant_slug (slug)
UNIQUE KEY uk_blog_tenant_email (email)
INDEX idx_blog_tenant_status (status)

-- blog_website
UNIQUE KEY uk_blog_website_domain (domain)
UNIQUE KEY uk_blog_website_tenant_slug (tenant_id, slug)
INDEX idx_blog_website_tenant_id (tenant_id)

-- blog_user
UNIQUE KEY uk_blog_user_email_website (email, website_id)
INDEX idx_blog_user_website_id (website_id)
INDEX idx_blog_user_status (status)
```

### Content Module Indexes
```sql
-- blog_post
UNIQUE KEY uk_blog_post_slug_website (slug, website_id)
INDEX idx_blog_post_website_status (website_id, status)
INDEX idx_blog_post_website_published (website_id, published_at)
INDEX idx_blog_post_author_id (author_id)
INDEX idx_blog_post_category_id (category_id)
FULLTEXT idx_blog_post_search (title, content)

-- blog_category
UNIQUE KEY uk_blog_category_slug_website (slug, website_id)
INDEX idx_blog_category_website_id (website_id)
INDEX idx_blog_category_nested_set (lft, rgt)

-- blog_tag
UNIQUE KEY uk_blog_tag_slug_website (slug, website_id)
INDEX idx_blog_tag_website_id (website_id)
```

### Feature Module Indexes
```sql
-- blog_media
INDEX idx_blog_media_website_id (website_id)
INDEX idx_blog_media_folder_path (folder_path)
INDEX idx_blog_media_mime_type (mime_type)

-- blog_notification
INDEX idx_blog_notification_website_id (website_id)
INDEX idx_blog_notification_user_id (user_id)
INDEX idx_blog_notification_status_scheduled (status, scheduled_at)

-- blog_payment
INDEX idx_blog_payment_website_id (website_id)
INDEX idx_blog_payment_customer_id (customer_id)
INDEX idx_blog_payment_status (status)
```

### Performance Optimization Indexes
```sql
-- Multi-tenant query optimization
INDEX idx_blog_post_list (website_id, status, published_at, id)
INDEX idx_blog_user_session_active (user_id, is_active, expires_at)
INDEX idx_blog_notification_pending (website_id, status, scheduled_at)
INDEX idx_blog_analytics_event_website_date (website_id, created_at)
```

## Data Size Estimates by Module

### Core System Storage (per website)

| Module | Table | Estimated Records | Storage per Record | Total Storage |
|--------|-------|------------------|-------------------|---------------|
| **Core** | blog_user | 1,000 - 100,000 | 1KB | 1MB - 100MB |
| | blog_user_session | 5,000 - 500,000 | 500B | 2.5MB - 250MB |
| | blog_role | 10 - 100 | 1KB | 10KB - 100KB |
| **Content** | blog_post | 100 - 50,000 | 10KB | 1MB - 500MB |
| | blog_category | 10 - 1,000 | 1KB | 10KB - 1MB |
| | blog_tag | 50 - 5,000 | 500B | 25KB - 2.5MB |
| | blog_comment | 1,000 - 500,000 | 500B | 500KB - 250MB |
| **Media** | blog_media | 500 - 250,000 | 2KB | 1MB - 500MB |
| **Notifications** | blog_notification | 10,000 - 1M | 1KB | 10MB - 1GB |
| **Analytics** | blog_analytics_event | 100,000 - 10M | 200B | 20MB - 2GB |
| | blog_activity_log | 50,000 - 5M | 300B | 15MB - 1.5GB |
| **Payment** | blog_payment | 100 - 100,000 | 1KB | 100KB - 100MB |
| **SEO** | blog_seo_meta | 1,000 - 100,000 | 1KB | 1MB - 100MB |

### Total Database Size by Website Type
- **Small Website** (< 10K users): 50MB - 200MB
- **Medium Website** (10K - 100K users): 500MB - 5GB
- **Large Website** (100K - 1M users): 5GB - 50GB
- **Enterprise** (1M+ users): 50GB+

### Module Growth Patterns
- **Core System**: Linear growth with user base
- **Content**: Moderate growth, depends on publishing frequency
- **Media**: High growth, largest storage consumer
- **Analytics**: Exponential growth, requires archiving strategy
- **Notifications**: High volume, short retention period
- **Payment**: Low volume, long retention required

## Performance Considerations

### Query Optimization
1. **Always filter by website_id first**
2. **Use covering indexes for common queries**
3. **Implement query result caching**
4. **Use read replicas for reporting**

### Scaling Strategies
1. **Horizontal sharding by website_id**
2. **Separate read/write databases**
3. **Archive old data to cold storage**
4. **Implement connection pooling**

### Maintenance
1. **Regular index optimization**
2. **Partition large tables by date**
3. **Automated backup strategies**
4. **Monitor slow query logs**